"""
Phase 3 Demo: Enhanced WebSocket Client Integration
Show how WebSocket coordinates with SimpleTokenManager
"""

import sys
import os
import logging
import time
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from src.common.auth.simple_token_manager import SimpleTokenManager, ServerAuthState

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class MockController:
    """Mock Controller for demo"""
    def __init__(self, server_ip, should_refresh_succeed=True):
        self.server_ip = server_ip
        self.should_refresh_succeed = should_refresh_succeed
        self.access_token = f"refreshed_token_{server_ip}"
    
    def refresh_access_token(self):
        if self.should_refresh_succeed:
            logger.info(f"🔄 Controller: Token refresh successful for {self.server_ip}")
            return self.access_token
        else:
            logger.warning(f"❌ Controller: Token refresh failed for {self.server_ip}")
            return None


class MockEnhancedWebsocketClient:
    """Mock Enhanced WebSocket Client for demo"""
    
    def __init__(self, url: str, header=None, server_ip=None):
        self.url = url
        self.header = header or {}
        self.server_ip = server_ip
        self.token_manager = SimpleTokenManager.get_instance()
        self.server_id = f"{server_ip}:8080"
        self._reconnect_enabled = True
        self._connection_attempts = 0
        self._max_connection_attempts = 3
        self.connected = False
        
        logger.info(f"🌐 MockWebSocket created for {self.server_id}")
    
    def connect(self):
        """Mock connect with server state checking"""
        if not self.token_manager.is_server_connected(self.server_id):
            logger.warning(f"⚠️ Server {self.server_id} not authenticated, skipping WebSocket connection")
            return False
        
        self._update_auth_headers()
        logger.info(f"🔗 WebSocket connected to {self.server_id}")
        self.connected = True
        self._connection_attempts = 0
        return True
    
    def connect_background(self):
        """Mock background connect"""
        return self.connect()
    
    def _update_auth_headers(self):
        """Update headers with current token"""
        access_token = self.token_manager.get_access_token(self.server_id)
        if access_token:
            self.header['Authorization'] = f"Bearer {access_token}"
            logger.debug(f"🔑 Updated WebSocket headers for {self.server_id}")
    
    def on_error_401(self):
        """Simulate 401 error handling"""
        logger.warning(f"🚫 WebSocket 401 error for {self.server_id}")
        
        if self.token_manager.is_server_connected(self.server_id):
            # Try refresh through token manager
            mock_controller = MockController(self.server_ip, should_refresh_succeed=True)
            
            if self.token_manager.try_refresh_token(self.server_id, mock_controller):
                self._update_auth_headers()
                logger.info(f"✅ Token refreshed for WebSocket {self.server_id}")
                return True
            else:
                logger.warning(f"❌ Token refresh failed for WebSocket {self.server_id}")
                self._disable_reconnect("Token refresh failed")
                return False
        else:
            logger.warning(f"⚠️ Server {self.server_id} disconnected, not attempting refresh")
            self._disable_reconnect("Server disconnected")
            return False
    
    def on_close(self):
        """Mock close handling"""
        logger.info(f"🔌 WebSocket closed for {self.server_id}")
        self.connected = False
        
        if self._should_reconnect():
            logger.info(f"🔄 Attempting WebSocket reconnect for {self.server_id}")
            time.sleep(0.1)  # Short delay for demo
            return self.connect_background()
        else:
            logger.info(f"⚠️ Not reconnecting WebSocket for {self.server_id}")
            return False
    
    def _should_reconnect(self) -> bool:
        """Check if should reconnect"""
        if not self._reconnect_enabled:
            return False
        if not self.token_manager.is_server_connected(self.server_id):
            return False
        if self._connection_attempts >= self._max_connection_attempts:
            return False
        return True
    
    def _disable_reconnect(self, reason: str):
        """Disable reconnection"""
        logger.warning(f"🚫 Disabling WebSocket reconnect for {self.server_id}: {reason}")
        self._reconnect_enabled = False
    
    def get_connection_status(self) -> dict:
        """Get connection status"""
        return {
            "server_id": self.server_id,
            "server_connected": self.token_manager.is_server_connected(self.server_id),
            "server_state": self.token_manager.get_server_state(self.server_id).value,
            "websocket_connected": self.connected,
            "reconnect_enabled": self._reconnect_enabled,
            "connection_attempts": self._connection_attempts,
            "has_auth_header": 'Authorization' in self.header
        }


def demo_websocket_server_coordination():
    """Demo WebSocket coordination with server authentication state"""
    print("\n" + "="*70)
    print("🎬 DEMO: WebSocket Server State Coordination")
    print("="*70)
    
    # Setup
    tm = SimpleTokenManager.get_instance()
    tm.clear_all_servers()
    
    server_ip = "*************"
    server_id = f"{server_ip}:8080"
    
    # Login and store tokens
    tm.store_tokens(
        server_id=server_id,
        access_token="websocket_access_token",
        refresh_token="websocket_refresh_token",
        expires_in=3600
    )
    
    # Create WebSocket client
    ws_client = MockEnhancedWebsocketClient(
        url=f"wss://{server_ip}:8081/socket",
        header={"Authorization": "Bearer websocket_access_token"},
        server_ip=server_ip
    )
    
    # Show initial status
    status = ws_client.get_connection_status()
    print(f"📊 Initial Status:")
    print(f"   Server connected: {status['server_connected']}")
    print(f"   WebSocket connected: {status['websocket_connected']}")
    
    # Connect WebSocket
    ws_client.connect()
    
    # Show connected status
    status = ws_client.get_connection_status()
    print(f"📊 After Connect:")
    print(f"   Server connected: {status['server_connected']}")
    print(f"   WebSocket connected: {status['websocket_connected']}")
    
    # Disconnect server (simulate refresh failure)
    tm._disconnect_server(server_id, "Demo disconnect")
    
    # Try to reconnect - should fail
    result = ws_client.on_close()
    
    # Show final status
    status = ws_client.get_connection_status()
    print(f"📊 After Server Disconnect:")
    print(f"   Server connected: {status['server_connected']}")
    print(f"   WebSocket connected: {status['websocket_connected']}")
    print(f"   Reconnect enabled: {status['reconnect_enabled']}")


def demo_websocket_token_refresh():
    """Demo WebSocket token refresh coordination"""
    print("\n" + "="*70)
    print("🎬 DEMO: WebSocket Token Refresh Coordination")
    print("="*70)
    
    tm = SimpleTokenManager.get_instance()
    server_ip = "*************"
    server_id = f"{server_ip}:8080"
    
    # Store tokens that expire soon
    tm.store_tokens(
        server_id=server_id,
        access_token="old_websocket_token",
        refresh_token="websocket_refresh_token",
        expires_in=240  # 4 minutes - will trigger proactive refresh
    )
    
    # Create WebSocket client
    ws_client = MockEnhancedWebsocketClient(
        url=f"wss://{server_ip}:8081/socket",
        server_ip=server_ip
    )
    
    # Connect
    ws_client.connect()
    
    # Show token info
    token_info = tm.get_token_info(server_id)
    print(f"📊 Token Status:")
    print(f"   Expires soon: {token_info.expires_soon()}")
    print(f"   Current token: {tm.get_access_token(server_id)}")
    
    # Simulate 401 error - should trigger refresh
    print(f"\n🔍 Simulating 401 error (should trigger token refresh)...")
    success = ws_client.on_error_401()
    
    # Show updated status
    print(f"📊 After Token Refresh:")
    print(f"   Refresh successful: {success}")
    print(f"   New token: {tm.get_access_token(server_id)}")
    print(f"   WebSocket header: {ws_client.header.get('Authorization', 'None')}")


def demo_websocket_failed_refresh():
    """Demo WebSocket behavior when token refresh fails"""
    print("\n" + "="*70)
    print("🎬 DEMO: WebSocket Failed Refresh → Stop Reconnecting")
    print("="*70)
    
    tm = SimpleTokenManager.get_instance()
    server_ip = "192.168.1.fail"  # Will trigger refresh failure
    server_id = f"{server_ip}:8080"
    
    # Store tokens
    tm.store_tokens(
        server_id=server_id,
        access_token="websocket_token",
        refresh_token="websocket_refresh",
        expires_in=3600
    )
    
    # Create WebSocket client
    ws_client = MockEnhancedWebsocketClient(
        url=f"wss://{server_ip}:8081/socket",
        server_ip=server_ip
    )
    
    # Connect
    ws_client.connect()
    
    # Show initial status
    status = ws_client.get_connection_status()
    print(f"📊 Initial Status:")
    print(f"   Server state: {status['server_state']}")
    print(f"   WebSocket connected: {status['websocket_connected']}")
    print(f"   Reconnect enabled: {status['reconnect_enabled']}")
    
    # Simulate 401 error with failed refresh
    print(f"\n🔍 Simulating 401 error (refresh will fail)...")
    
    # Mock failed controller
    class FailedController:
        def __init__(self, server_ip):
            self.server_ip = server_ip
        def refresh_access_token(self):
            return None
    
    # Try refresh with failed controller
    failed_controller = FailedController(server_ip)
    refresh_success = tm.try_refresh_token(server_id, failed_controller)
    
    # Show final status
    status = ws_client.get_connection_status()
    print(f"📊 After Failed Refresh:")
    print(f"   Refresh successful: {refresh_success}")
    print(f"   Server state: {status['server_state']}")
    print(f"   Server connected: {status['server_connected']}")
    print(f"   Reconnect enabled: {status['reconnect_enabled']}")
    
    # Try to reconnect - should not attempt
    print(f"\n🔍 Attempting reconnect (should be blocked)...")
    reconnect_result = ws_client.on_close()
    print(f"   Reconnect attempted: {reconnect_result}")


def demo_multiple_websocket_servers():
    """Demo multiple WebSocket servers with different states"""
    print("\n" + "="*70)
    print("🎬 DEMO: Multiple WebSocket Servers")
    print("="*70)
    
    tm = SimpleTokenManager.get_instance()
    tm.clear_all_servers()
    
    # Setup multiple servers
    servers = [
        ("*************", "Server A - Good"),
        ("*************", "Server B - Token expires soon"),
        ("192.168.1.fail", "Server C - Will fail refresh")
    ]
    
    ws_clients = []
    
    # Create and connect all WebSocket clients
    for server_ip, description in servers:
        server_id = f"{server_ip}:8080"
        
        # Store tokens
        if "expires soon" in description:
            expires_in = 240  # 4 minutes
        else:
            expires_in = 3600  # 1 hour
        
        tm.store_tokens(
            server_id=server_id,
            access_token=f"token_{server_ip}",
            refresh_token=f"refresh_{server_ip}",
            expires_in=expires_in
        )
        
        # Create WebSocket client
        ws_client = MockEnhancedWebsocketClient(
            url=f"wss://{server_ip}:8081/socket",
            server_ip=server_ip
        )
        
        ws_client.connect()
        ws_clients.append((ws_client, description))
        
        print(f"✅ {description}: WebSocket connected")
    
    # Show all server statuses
    print(f"\n📊 All Server Statuses:")
    for server_id, server_info in tm.get_all_servers().items():
        status = "✅ Connected" if server_info.auth_state == ServerAuthState.CONNECTED else "❌ Disconnected"
        print(f"   {server_id}: {status}")
    
    # Simulate various scenarios
    print(f"\n🔍 Simulating various scenarios...")
    
    # Server B: Token refresh (should succeed)
    print(f"   Server B: Simulating token refresh...")
    ws_clients[1][0].on_error_401()
    
    # Server C: Token refresh failure (should disconnect)
    print(f"   Server C: Simulating refresh failure...")
    failed_controller = MockController(servers[2][0], should_refresh_succeed=False)
    tm.try_refresh_token(f"{servers[2][0]}:8080", failed_controller)
    
    # Show final statuses
    print(f"\n📊 Final Server Statuses:")
    for i, (ws_client, description) in enumerate(ws_clients):
        status = ws_client.get_connection_status()
        state_icon = "✅" if status['server_connected'] else "❌"
        ws_icon = "🔗" if status['websocket_connected'] else "🔌"
        print(f"   {description}: {state_icon} Server, {ws_icon} WebSocket")


def demo_websocket_monitoring():
    """Demo WebSocket connection monitoring"""
    print("\n" + "="*70)
    print("🎬 DEMO: WebSocket Connection Monitoring")
    print("="*70)
    
    tm = SimpleTokenManager.get_instance()
    server_ip = "192.168.1.monitor"
    server_id = f"{server_ip}:8080"
    
    # Store tokens
    tm.store_tokens(
        server_id=server_id,
        access_token="monitor_token",
        refresh_token="monitor_refresh",
        expires_in=600  # 10 minutes
    )
    
    # Create WebSocket client
    ws_client = MockEnhancedWebsocketClient(
        url=f"wss://{server_ip}:8081/socket",
        header={"Authorization": "Bearer monitor_token"},
        server_ip=server_ip
    )
    
    # Connect
    ws_client.connect()
    
    # Show detailed status
    status = ws_client.get_connection_status()
    print(f"📊 Detailed WebSocket Status:")
    print(f"   Server ID: {status['server_id']}")
    print(f"   Server Connected: {status['server_connected']}")
    print(f"   Server State: {status['server_state']}")
    print(f"   WebSocket Connected: {status['websocket_connected']}")
    print(f"   Reconnect Enabled: {status['reconnect_enabled']}")
    print(f"   Connection Attempts: {status['connection_attempts']}")
    print(f"   Has Auth Header: {status['has_auth_header']}")
    
    # Show token manager overview
    print(f"\n📈 Token Manager Overview:")
    print(f"   Total servers: {len(tm.get_all_servers())}")
    print(f"   Connected servers: {len(tm.get_connected_servers())}")
    
    # Show token details
    token_info = tm.get_token_info(server_id)
    if token_info:
        print(f"   Token expires at: {token_info.expires_at}")
        print(f"   Token expires soon: {token_info.expires_soon()}")
        print(f"   Time until expiry: {token_info.time_until_expiry()}")


def main():
    """Run all Phase 3 demos"""
    print("🚀 Phase 3 Demo: Enhanced WebSocket Client Integration")
    print("Solving: WebSocket coordination with token management")
    
    try:
        demo_websocket_server_coordination()
        demo_websocket_token_refresh()
        demo_websocket_failed_refresh()
        demo_multiple_websocket_servers()
        demo_websocket_monitoring()
        
        print("\n" + "="*70)
        print("🎉 ALL PHASE 3 DEMOS COMPLETED!")
        print("="*70)
        
        print("\n✅ Key Problems SOLVED:")
        print("   🔗 WebSocket coordinates with server authentication state")
        print("   🚫 Stop reconnecting when server is disconnected")
        print("   🔄 Coordinated token refresh between API and WebSocket")
        print("   📊 Comprehensive connection status monitoring")
        print("   🛡️ Better error handling and resource management")
        
        print("\n🎯 Benefits:")
        print("   • No more wasted WebSocket reconnection attempts")
        print("   • Coordinated token management across all components")
        print("   • Better user experience with clear connection states")
        print("   • Efficient resource utilization")
        print("   • Graceful handling of authentication failures")
        
    except Exception as e:
        print(f"\n❌ DEMO FAILED: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
