"""
Phase 4 Demo: UI Integration with Authentication Management
Show how UI components integrate with SimpleTokenManager
"""

import sys
import os
import logging
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from src.common.auth.simple_token_manager import SimpleTokenManager, ServerAuthState
from src.common.auth.auth_ui_manager import AuthUIManager

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class MockMainWindow:
    """Mock main window for demo"""
    def __init__(self):
        self.width_val = 1200
        self.height_val = 800
        self.notifications = []
    
    def width(self):
        return self.width_val
    
    def height(self):
        return self.height_val


class MockNotifications:
    """Mock notifications for demo"""
    def __init__(self, parent=None, title=None, icon=None, time=3000):
        self.parent = parent
        self.title = title
        self.icon = icon
        self.time = time
        
        # Store notification for demo
        if parent and hasattr(parent, 'notifications'):
            parent.notifications.append({
                'title': title,
                'icon': icon,
                'time': time,
                'timestamp': datetime.now()
            })
        
        logger.info(f"📢 Notification: {title}")


def demo_ui_notifications():
    """Demo UI notification system"""
    print("\n" + "="*70)
    print("🎬 DEMO: UI Notification System")
    print("="*70)
    
    # Setup
    tm = SimpleTokenManager.get_instance()
    tm.clear_all_servers()
    
    # Create mock main window
    main_window = MockMainWindow()
    
    # Create AuthUIManager and set parent
    auth_ui = AuthUIManager.get_instance()
    auth_ui.set_notification_parent(main_window)
    
    server_id = "*************:8080"
    
    # Patch notifications
    with patch('src.common.auth.auth_ui_manager.Notifications', MockNotifications):
        
        # Demo 1: Login required notification
        print("📢 Showing login required notification...")
        auth_ui.show_login_required_notification(server_id, "Session expired")
        
        # Demo 2: Successful token refresh
        print("📢 Showing token refresh success...")
        auth_ui.show_token_refresh_notification(server_id, True)
        
        # Demo 3: Failed token refresh
        print("📢 Showing token refresh failure...")
        auth_ui.show_token_refresh_notification(server_id, False)
        
        # Demo 4: Server connection status
        print("📢 Showing server connection status...")
        auth_ui.show_server_connection_notification(server_id, True, "Login successful")
        auth_ui.show_server_connection_notification(server_id, False, "Connection lost")
        
        # Demo 5: Authentication error
        print("📢 Showing authentication error...")
        auth_ui.show_authentication_error(server_id, "Invalid credentials")
    
    # Show notification summary
    print(f"\n📊 Total notifications shown: {len(main_window.notifications)}")
    for i, notif in enumerate(main_window.notifications, 1):
        print(f"   {i}. {notif['title']} ({notif['timestamp'].strftime('%H:%M:%S')})")


def demo_server_status_display():
    """Demo server status display functionality"""
    print("\n" + "="*70)
    print("🎬 DEMO: Server Status Display")
    print("="*70)
    
    tm = SimpleTokenManager.get_instance()
    tm.clear_all_servers()
    
    auth_ui = AuthUIManager.get_instance()
    
    # Setup multiple servers with different states
    servers = [
        ("*************:8080", "Server A - Production", "connected", 3600),
        ("*************:8080", "Server B - Staging", "connected", 300),  # Expires soon
        ("*************:8080", "Server C - Development", "disconnected", None),
        ("*************:8080", "Server D - Testing", "refreshing", 1800)
    ]
    
    # Setup servers
    for server_id, name, state, expires_in in servers:
        if state == "connected":
            tm.store_tokens(
                server_id=server_id,
                access_token=f"token_{server_id.split(':')[0]}",
                refresh_token=f"refresh_{server_id.split(':')[0]}",
                expires_in=expires_in
            )
        elif state == "disconnected":
            tm._disconnect_server(server_id, "Connection timeout")
        elif state == "refreshing":
            tm.store_tokens(
                server_id=server_id,
                access_token=f"token_{server_id.split(':')[0]}",
                refresh_token=f"refresh_{server_id.split(':')[0]}",
                expires_in=expires_in
            )
            # Set to refreshing state
            tm._servers[server_id].auth_state = ServerAuthState.REFRESHING
    
    # Get and display status summary
    summary = auth_ui.get_server_status_summary()
    
    print(f"📊 Server Status Summary:")
    print(f"   Total servers: {summary['total_servers']}")
    print(f"   Connected: {summary['connected_count']}")
    print(f"   Disconnected: {summary['disconnected_count']}")
    
    print(f"\n📋 Individual Server Status:")
    for server_id, info in summary['servers'].items():
        status_icon = "🟢" if info['connected'] else "🔴"
        status_text = info['state'].title()
        
        print(f"   {status_icon} {info['friendly_name']} ({server_id})")
        print(f"      Status: {status_text}")
        
        if info['error']:
            print(f"      Error: {info['error']}")
        
        # Show token expiration info
        token_info = tm.get_token_info(server_id)
        if token_info and token_info.expires_at:
            time_left = token_info.time_until_expiry()
            if time_left:
                hours = int(time_left.total_seconds() // 3600)
                minutes = int((time_left.total_seconds() % 3600) // 60)
                expires_soon_indicator = " ⚠️" if token_info.expires_soon() else ""
                print(f"      Token expires: {hours}h {minutes}m{expires_soon_indicator}")


def demo_ui_signal_system():
    """Demo UI signal system"""
    print("\n" + "="*70)
    print("🎬 DEMO: UI Signal System")
    print("="*70)
    
    auth_ui = AuthUIManager.get_instance()
    
    # Mock UI components that listen to signals
    class MockUIComponent:
        def __init__(self, name):
            self.name = name
            self.events = []
        
        def on_login_required(self, server_id, reason):
            event = f"Login required for {server_id}: {reason}"
            self.events.append(event)
            logger.info(f"🎯 {self.name} received: {event}")
        
        def on_server_status_changed(self, server_id, status):
            event = f"Server {server_id} status: {status}"
            self.events.append(event)
            logger.info(f"🎯 {self.name} received: {event}")
        
        def on_token_refreshed(self, server_id):
            event = f"Token refreshed for {server_id}"
            self.events.append(event)
            logger.info(f"🎯 {self.name} received: {event}")
        
        def on_auth_error(self, server_id, error):
            event = f"Auth error for {server_id}: {error}"
            self.events.append(event)
            logger.info(f"🎯 {self.name} received: {event}")
    
    # Create mock UI components
    main_screen = MockUIComponent("MainScreen")
    server_list = MockUIComponent("ServerList")
    status_bar = MockUIComponent("StatusBar")
    
    # Connect signals
    auth_ui.login_required.connect(main_screen.on_login_required)
    auth_ui.login_required.connect(server_list.on_login_required)
    
    auth_ui.server_status_changed.connect(main_screen.on_server_status_changed)
    auth_ui.server_status_changed.connect(status_bar.on_server_status_changed)
    
    auth_ui.token_refreshed.connect(main_screen.on_token_refreshed)
    auth_ui.authentication_error.connect(main_screen.on_auth_error)
    
    server_id = "192.168.1.signal:8080"
    
    # Emit signals
    print("📡 Emitting UI signals...")
    
    with patch('src.common.auth.auth_ui_manager.Notifications', MockNotifications):
        auth_ui.show_login_required_notification(server_id, "Demo login required")
        auth_ui.show_server_connection_notification(server_id, True)
        auth_ui.show_token_refresh_notification(server_id, True)
        auth_ui.show_authentication_error(server_id, "Demo auth error")
    
    # Show signal reception summary
    print(f"\n📊 Signal Reception Summary:")
    for component in [main_screen, server_list, status_bar]:
        print(f"   {component.name}: {len(component.events)} events received")
        for event in component.events:
            print(f"     • {event}")


def demo_token_expiration_warnings():
    """Demo token expiration warnings in UI"""
    print("\n" + "="*70)
    print("🎬 DEMO: Token Expiration Warnings")
    print("="*70)
    
    tm = SimpleTokenManager.get_instance()
    tm.clear_all_servers()
    
    auth_ui = AuthUIManager.get_instance()
    
    # Create servers with different expiration times
    expiration_scenarios = [
        ("192.168.1.fresh:8080", "Fresh Token", 7200),      # 2 hours
        ("192.168.1.warning:8080", "Warning Token", 300),   # 5 minutes (warning)
        ("192.168.1.critical:8080", "Critical Token", 60),  # 1 minute (critical)
        ("192.168.1.expired:8080", "Expired Token", -300)   # Expired 5 minutes ago
    ]
    
    print("⏰ Setting up tokens with different expiration times...")
    
    for server_id, description, expires_in_seconds in expiration_scenarios:
        if expires_in_seconds > 0:
            tm.store_tokens(
                server_id=server_id,
                access_token=f"token_{description.lower().replace(' ', '_')}",
                refresh_token=f"refresh_{description.lower().replace(' ', '_')}",
                expires_in=expires_in_seconds
            )
        else:
            # Create expired token
            tm.store_tokens(
                server_id=server_id,
                access_token=f"token_{description.lower().replace(' ', '_')}",
                refresh_token=f"refresh_{description.lower().replace(' ', '_')}",
                expires_in=3600
            )
            # Manually set expiration to past
            expired_time = datetime.now() + timedelta(seconds=expires_in_seconds)
            tm._servers[server_id].token.expires_at = expired_time
    
    print(f"\n📊 Token Expiration Status:")
    
    for server_id, description, _ in expiration_scenarios:
        token_info = tm.get_token_info(server_id)
        if token_info:
            print(f"\n🔑 {description} ({server_id}):")
            
            if token_info.expires_at:
                time_left = token_info.time_until_expiry()
                
                if token_info.is_expired():
                    print(f"   ❌ Status: EXPIRED")
                    print(f"   ⏰ Expired: {abs(int(time_left.total_seconds() // 60))} minutes ago")
                elif token_info.expires_soon():
                    print(f"   ⚠️ Status: EXPIRES SOON")
                    minutes = int(time_left.total_seconds() // 60)
                    print(f"   ⏰ Expires in: {minutes} minutes")
                else:
                    print(f"   ✅ Status: VALID")
                    hours = int(time_left.total_seconds() // 3600)
                    minutes = int((time_left.total_seconds() % 3600) // 60)
                    print(f"   ⏰ Expires in: {hours}h {minutes}m")
            else:
                print(f"   ❓ Status: NO EXPIRATION INFO")
    
    # Demo proactive warnings
    print(f"\n⚠️ Proactive Expiration Warnings:")
    
    with patch('src.common.auth.auth_ui_manager.Notifications', MockNotifications):
        for server_id, description, _ in expiration_scenarios:
            token_info = tm.get_token_info(server_id)
            if token_info:
                if token_info.is_expired():
                    auth_ui.show_login_required_notification(server_id, "Token expired")
                elif token_info.expires_soon():
                    auth_ui.show_token_refresh_notification(server_id, True)


def demo_error_recovery_ui():
    """Demo error recovery UI flow"""
    print("\n" + "="*70)
    print("🎬 DEMO: Error Recovery UI Flow")
    print("="*70)
    
    tm = SimpleTokenManager.get_instance()
    tm.clear_all_servers()
    
    auth_ui = AuthUIManager.get_instance()
    
    server_id = "192.168.1.recovery:8080"
    
    # Simulate error recovery flow
    print("🔄 Simulating error recovery flow...")
    
    with patch('src.common.auth.auth_ui_manager.Notifications', MockNotifications):
        
        # Step 1: Initial connection
        print("\n1️⃣ Initial connection successful")
        tm.store_tokens(
            server_id=server_id,
            access_token="initial_token",
            refresh_token="initial_refresh",
            expires_in=300  # 5 minutes
        )
        auth_ui.show_server_connection_notification(server_id, True, "Initial connection")
        
        # Step 2: Token expires soon - proactive refresh
        print("\n2️⃣ Token expires soon - attempting proactive refresh")
        auth_ui.show_token_refresh_notification(server_id, True)
        
        # Step 3: Refresh fails - server disconnected
        print("\n3️⃣ Refresh fails - server disconnected")
        tm._disconnect_server(server_id, "Refresh token expired")
        auth_ui.show_server_connection_notification(server_id, False, "Refresh failed")
        auth_ui.show_login_required_notification(server_id, "Please login again")
        
        # Step 4: User logs in again
        print("\n4️⃣ User logs in again")
        tm.store_tokens(
            server_id=server_id,
            access_token="new_token_after_login",
            refresh_token="new_refresh_after_login",
            expires_in=3600  # 1 hour
        )
        auth_ui.show_server_connection_notification(server_id, True, "Login successful")
        
        # Step 5: System recovered
        print("\n5️⃣ System fully recovered")
        summary = auth_ui.get_server_status_summary()
        print(f"   📊 Connected servers: {summary['connected_count']}")
        print(f"   📊 Disconnected servers: {summary['disconnected_count']}")


def main():
    """Run all Phase 4 demos"""
    print("🚀 Phase 4 Demo: UI Integration with Authentication Management")
    print("Solving: User-friendly authentication UI and notifications")
    
    try:
        demo_ui_notifications()
        demo_server_status_display()
        demo_ui_signal_system()
        demo_token_expiration_warnings()
        demo_error_recovery_ui()
        
        print("\n" + "="*70)
        print("🎉 ALL PHASE 4 DEMOS COMPLETED!")
        print("="*70)
        
        print("\n✅ Key Problems SOLVED:")
        print("   📢 User-friendly notifications for authentication events")
        print("   📊 Real-time server status display with token information")
        print("   ⚠️ Proactive token expiration warnings")
        print("   📡 Signal-based UI updates across components")
        print("   🔄 Clear error recovery flow guidance")
        
        print("\n🎯 Benefits:")
        print("   • Users always know authentication status")
        print("   • Proactive warnings prevent unexpected disconnections")
        print("   • Clear guidance for error recovery")
        print("   • Seamless integration with existing UI")
        print("   • Better debugging and monitoring capabilities")
        
        print("\n🏆 Complete OAuth 2.0 Solution:")
        print("   Phase 1: ✅ Core token management")
        print("   Phase 2: ✅ API client proactive refresh")
        print("   Phase 3: ✅ WebSocket coordination")
        print("   Phase 4: ✅ UI integration and user experience")
        
    except Exception as e:
        print(f"\n❌ DEMO FAILED: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
