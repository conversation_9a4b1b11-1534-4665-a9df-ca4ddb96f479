"""
Complete Integration Test - Verify OAuth 2.0 Solution Integration
Test that all components work together after integration
"""

import sys
import os
import logging
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_enhanced_api_client_integration():
    """Test Enhanced API Client integration in controller"""
    print("\n" + "="*60)
    print("🧪 TESTING Enhanced API Client Integration")
    print("="*60)
    
    try:
        # Test import
        from src.common.controller.controller_manager import Controller
        from src.common.server.server_info import ServerInfoModel
        
        # Create mock server
        server_info = ServerInfoModel()
        server_info.data.server_ip = "*************"
        server_info.data.server_port = 8080
        server_info.data.username = "admin"
        server_info.data.password = "password"
        
        # Create controller (should use EnhancedAPIClient now)
        controller = Controller(server=server_info)
        
        # Verify it's using EnhancedAPIClient
        from src.api.enhanced_api_client import EnhancedAPIClient
        assert isinstance(controller.api_client, EnhancedAPIClient), "Controller should use EnhancedAPIClient"
        
        print("✅ Controller uses EnhancedAPIClient")
        print(f"✅ Server ID: {controller.api_client.server_id}")
        print(f"✅ Token Manager: {controller.api_client.token_manager}")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced API Client integration failed: {e}")
        return False


def test_enhanced_websocket_integration():
    """Test Enhanced WebSocket Client integration"""
    print("\n" + "="*60)
    print("🧪 TESTING Enhanced WebSocket Client Integration")
    print("="*60)
    
    try:
        # Test import
        from src.common.websocket.enhanced_websocket_client import EnhancedWebsocketClient
        
        # Create enhanced WebSocket client
        ws_client = EnhancedWebsocketClient(
            url="wss://*************:8081/socket",
            header={"Authorization": "Bearer test_token"},
            server_ip="*************"
        )
        
        print("✅ EnhancedWebsocketClient created successfully")
        print(f"✅ Server ID: {ws_client.server_id}")
        print(f"✅ Token Manager: {ws_client.token_manager}")
        print(f"✅ Reconnect enabled: {ws_client._reconnect_enabled}")
        
        # Test connection status
        status = ws_client.get_connection_status()
        print(f"✅ Connection status: {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced WebSocket integration failed: {e}")
        return False


def test_auth_ui_manager_integration():
    """Test Auth UI Manager integration"""
    print("\n" + "="*60)
    print("🧪 TESTING Auth UI Manager Integration")
    print("="*60)
    
    try:
        # Test import
        from src.common.auth.auth_ui_manager import AuthUIManager
        
        # Create UI manager
        auth_ui = AuthUIManager.get_instance()
        
        print("✅ AuthUIManager created successfully")
        print(f"✅ Token Manager: {auth_ui.token_manager}")
        
        # Test server status summary
        summary = auth_ui.get_server_status_summary()
        print(f"✅ Server status summary: {summary}")
        
        return True
        
    except Exception as e:
        print(f"❌ Auth UI Manager integration failed: {e}")
        return False


def test_token_manager_coordination():
    """Test token manager coordination between components"""
    print("\n" + "="*60)
    print("🧪 TESTING Token Manager Coordination")
    print("="*60)
    
    try:
        from src.common.auth.simple_token_manager import SimpleTokenManager
        from src.api.enhanced_api_client import EnhancedAPIClient
        from src.common.websocket.enhanced_websocket_client import EnhancedWebsocketClient
        from src.common.auth.auth_ui_manager import AuthUIManager
        from src.common.server.server_info import ServerInfoModel
        
        # Clear token manager
        tm = SimpleTokenManager.get_instance()
        tm.clear_all_servers()
        
        # Create server info
        server_info = ServerInfoModel()
        server_info.data.server_ip = "192.168.1.test"
        server_info.data.server_port = 8080
        
        # Create components
        api_client = EnhancedAPIClient(server=server_info)
        ws_client = EnhancedWebsocketClient(
            url="wss://192.168.1.test:8081/socket",
            server_ip="192.168.1.test"
        )
        auth_ui = AuthUIManager.get_instance()
        
        # Verify they all use the same token manager instance
        assert api_client.token_manager is tm, "API client should use same token manager"
        assert ws_client.token_manager is tm, "WebSocket client should use same token manager"
        assert auth_ui.token_manager is tm, "Auth UI should use same token manager"
        
        print("✅ All components use same token manager instance")
        
        # Test token storage and retrieval
        server_id = "192.168.1.test:8080"
        tm.store_tokens(
            server_id=server_id,
            access_token="test_access_token",
            refresh_token="test_refresh_token",
            expires_in=3600
        )
        
        # Verify all components can access the token
        api_token = api_client.token_manager.get_access_token(server_id)
        ws_token = ws_client.token_manager.get_access_token(server_id)
        
        assert api_token == "test_access_token", "API client should get correct token"
        assert ws_token == "test_access_token", "WebSocket client should get correct token"
        
        print("✅ Token coordination works between components")
        
        # Test server status
        assert api_client.token_manager.is_server_connected(server_id), "Server should be connected"
        assert ws_client.token_manager.is_server_connected(server_id), "Server should be connected"
        
        print("✅ Server status coordination works")
        
        return True
        
    except Exception as e:
        print(f"❌ Token manager coordination failed: {e}")
        return False


def test_backward_compatibility():
    """Test that existing code still works"""
    print("\n" + "="*60)
    print("🧪 TESTING Backward Compatibility")
    print("="*60)
    
    try:
        # Test that original imports still work
        from src.api.api_client import APIClient
        from src.common.websocket.websocket_client import WebsocketClient
        
        print("✅ Original APIClient import works")
        print("✅ Original WebsocketClient import works")
        
        # Test that enhanced imports work alongside
        from src.api.enhanced_api_client import EnhancedAPIClient
        from src.common.websocket.enhanced_websocket_client import EnhancedWebsocketClient
        
        print("✅ Enhanced imports work alongside original")
        
        # Test that EnhancedAPIClient is subclass of APIClient
        assert issubclass(EnhancedAPIClient, APIClient), "EnhancedAPIClient should inherit from APIClient"
        assert issubclass(EnhancedWebsocketClient, WebsocketClient), "EnhancedWebsocketClient should inherit from WebsocketClient"
        
        print("✅ Enhanced classes properly inherit from original classes")
        
        return True
        
    except Exception as e:
        print(f"❌ Backward compatibility test failed: {e}")
        return False


def test_main_screen_integration():
    """Test MainScreen integration (without actually creating UI)"""
    print("\n" + "="*60)
    print("🧪 TESTING MainScreen Integration")
    print("="*60)
    
    try:
        # Test that MainScreen can import all required components
        from src.presentation.main_screen.main_screen import MainScreen
        from src.common.auth.auth_ui_manager import AuthUIManager
        from src.common.websocket.enhanced_websocket_client import EnhancedWebsocketClient
        
        print("✅ MainScreen imports work")
        print("✅ All OAuth 2.0 components can be imported")
        
        # Note: We can't actually create MainScreen without PySide6 UI context
        # But we can verify the imports work
        
        return True
        
    except Exception as e:
        print(f"❌ MainScreen integration test failed: {e}")
        return False


def test_file_structure():
    """Test that all required files exist and are accessible"""
    print("\n" + "="*60)
    print("🧪 TESTING File Structure")
    print("="*60)
    
    required_files = [
        "src/common/auth/simple_token_manager.py",
        "src/common/auth/auth_ui_manager.py",
        "src/api/enhanced_api_client.py",
        "src/common/websocket/enhanced_websocket_client.py",
        "src/common/widget/server_status_widget.py",
        "src/presentation/server_screen/enhanced_login_dialog.py"
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - MISSING")
            all_exist = False
    
    return all_exist


def main():
    """Run complete integration test suite"""
    print("🚀 Complete OAuth 2.0 Integration Test Suite")
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 Working Directory: {os.getcwd()}")
    
    # Run all tests
    tests = [
        ("File Structure", test_file_structure),
        ("Enhanced API Client Integration", test_enhanced_api_client_integration),
        ("Enhanced WebSocket Integration", test_enhanced_websocket_integration),
        ("Auth UI Manager Integration", test_auth_ui_manager_integration),
        ("Token Manager Coordination", test_token_manager_coordination),
        ("Backward Compatibility", test_backward_compatibility),
        ("MainScreen Integration", test_main_screen_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} - CRASHED: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*60)
    print("📊 INTEGRATION TEST RESULTS")
    print("="*60)
    
    passed = 0
    failed = 0
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
        
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n📈 Overall Results:")
    print(f"   Passed: {passed}")
    print(f"   Failed: {failed}")
    print(f"   Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 ALL INTEGRATION TESTS PASSED!")
        print("\n✅ OAuth 2.0 Solution Successfully Integrated!")
        print("\n🚀 What's Working Now:")
        print("   • Enhanced API Client with automatic token management")
        print("   • Enhanced WebSocket Client with smart reconnection")
        print("   • Coordinated token management across all components")
        print("   • UI notifications for authentication events")
        print("   • Backward compatibility with existing code")
        
        print("\n🎯 Ready for Production:")
        print("   • All existing iVMS functionality preserved")
        print("   • Enhanced with OAuth 2.0 token management")
        print("   • Better error handling and user experience")
        print("   • Proactive token refresh prevents disconnections")
        
    else:
        print(f"\n⚠️ {failed} integration tests failed.")
        print("Check the errors above and ensure all files are properly created.")
    
    return failed == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
