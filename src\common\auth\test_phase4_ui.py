"""
Test Phase 4 UI Integration
Verify UI components work with SimpleTokenManager
"""

import sys
import os
import logging
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from src.common.auth.simple_token_manager import SimpleTokenManager, ServerAuthState
from src.common.auth.auth_ui_manager import AuthUIManager

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_auth_ui_manager_notifications():
    """Test AuthUIManager notification system"""
    print("\n" + "="*70)
    print("🧪 Testing AuthUIManager Notifications")
    print("="*70)
    
    # Clear token manager
    tm = SimpleTokenManager.get_instance()
    tm.clear_all_servers()
    
    # Create mock parent widget
    mock_parent = Mock()
    mock_parent.width.return_value = 800
    
    # Create AuthUIManager
    auth_ui = AuthUIManager.get_instance()
    auth_ui.set_notification_parent(mock_parent)
    
    server_id = "*************:8080"
    
    # Test login required notification
    with patch('src.common.auth.auth_ui_manager.Notifications') as mock_notifications:
        auth_ui.show_login_required_notification(server_id, "Token expired")
        
        # Verify notification was called
        mock_notifications.assert_called_once()
        call_args = mock_notifications.call_args
        assert "Login Required" in call_args[1]['title']
        
    print("✅ Login required notification works")
    
    # Test token refresh notification
    with patch('src.common.auth.auth_ui_manager.Notifications') as mock_notifications:
        auth_ui.show_token_refresh_notification(server_id, True)
        
        mock_notifications.assert_called_once()
        call_args = mock_notifications.call_args
        assert "Session Renewed" in call_args[1]['title']
        
    print("✅ Token refresh notification works")
    
    # Test server connection notification
    with patch('src.common.auth.auth_ui_manager.Notifications') as mock_notifications:
        auth_ui.show_server_connection_notification(server_id, False, "Connection lost")
        
        mock_notifications.assert_called_once()
        call_args = mock_notifications.call_args
        assert "Disconnected" in call_args[1]['title']
        
    print("✅ Server connection notification works")


def test_server_status_summary():
    """Test server status summary functionality"""
    print("\n" + "="*70)
    print("🧪 Testing Server Status Summary")
    print("="*70)
    
    tm = SimpleTokenManager.get_instance()
    tm.clear_all_servers()
    
    auth_ui = AuthUIManager.get_instance()
    
    # Add multiple servers with different states
    servers = [
        ("*************:8080", "connected"),
        ("*************:8080", "connected"),
        ("*************:8080", "disconnected")
    ]
    
    for server_id, state in servers:
        if state == "connected":
            tm.store_tokens(
                server_id=server_id,
                access_token=f"token_{server_id}",
                refresh_token=f"refresh_{server_id}",
                expires_in=3600
            )
        else:
            tm._disconnect_server(server_id, "Test disconnect")
    
    # Get status summary
    summary = auth_ui.get_server_status_summary()
    
    assert summary["total_servers"] == 3, f"Expected 3 servers, got {summary['total_servers']}"
    assert summary["connected_count"] == 2, f"Expected 2 connected, got {summary['connected_count']}"
    assert summary["disconnected_count"] == 1, f"Expected 1 disconnected, got {summary['disconnected_count']}"
    
    print("✅ Server status summary works")
    print(f"📊 Summary: {summary['connected_count']} connected, {summary['disconnected_count']} disconnected")


def test_ui_signal_emissions():
    """Test UI signal emissions"""
    print("\n" + "="*70)
    print("🧪 Testing UI Signal Emissions")
    print("="*70)
    
    auth_ui = AuthUIManager.get_instance()
    
    # Mock signal connections
    login_required_signals = []
    server_status_signals = []
    token_refresh_signals = []
    auth_error_signals = []
    
    def capture_login_required(server_id, reason):
        login_required_signals.append((server_id, reason))
    
    def capture_server_status(server_id, status):
        server_status_signals.append((server_id, status))
    
    def capture_token_refresh(server_id):
        token_refresh_signals.append(server_id)
    
    def capture_auth_error(server_id, error):
        auth_error_signals.append((server_id, error))
    
    # Connect signals
    auth_ui.login_required.connect(capture_login_required)
    auth_ui.server_status_changed.connect(capture_server_status)
    auth_ui.token_refreshed.connect(capture_token_refresh)
    auth_ui.authentication_error.connect(capture_auth_error)
    
    server_id = "192.168.1.test:8080"
    
    # Test signal emissions
    with patch('src.common.auth.auth_ui_manager.Notifications'):
        auth_ui.show_login_required_notification(server_id, "Test reason")
        auth_ui.show_server_connection_notification(server_id, True)
        auth_ui.show_token_refresh_notification(server_id, True)
        auth_ui.show_authentication_error(server_id, "Test error")
    
    # Verify signals were emitted
    assert len(login_required_signals) == 1, "Login required signal not emitted"
    assert len(server_status_signals) == 1, "Server status signal not emitted"
    assert len(token_refresh_signals) == 1, "Token refresh signal not emitted"
    assert len(auth_error_signals) == 1, "Auth error signal not emitted"
    
    print("✅ UI signal emissions work")
    print(f"📡 Signals captured: {len(login_required_signals)} login, {len(server_status_signals)} status, {len(token_refresh_signals)} refresh, {len(auth_error_signals)} error")


def test_server_status_widget_integration():
    """Test server status widget integration"""
    print("\n" + "="*70)
    print("🧪 Testing Server Status Widget Integration")
    print("="*70)
    
    tm = SimpleTokenManager.get_instance()
    tm.clear_all_servers()
    
    auth_ui = AuthUIManager.get_instance()
    
    server_id = "192.168.1.widget:8080"
    
    # Mock widget
    mock_widget = Mock()
    mock_widget.update_auth_status = Mock()
    
    # Register widget
    auth_ui.register_server_status_widget(server_id, mock_widget)
    
    # Store tokens
    tm.store_tokens(
        server_id=server_id,
        access_token="widget_token",
        refresh_token="widget_refresh",
        expires_in=3600
    )
    
    # Update status widgets
    auth_ui.update_server_status_widgets()
    
    # Verify widget was updated
    mock_widget.update_auth_status.assert_called_once()
    call_args = mock_widget.update_auth_status.call_args[0]
    assert call_args[0] == True, "Widget should show connected state"
    assert call_args[1] == "connected", "Widget should show connected status"
    
    print("✅ Server status widget integration works")


def test_friendly_server_names():
    """Test friendly server name resolution"""
    print("\n" + "="*70)
    print("🧪 Testing Friendly Server Names")
    print("="*70)
    
    auth_ui = AuthUIManager.get_instance()
    
    # Test with mock server info
    with patch('src.common.auth.auth_ui_manager.server_info_model_manager') as mock_manager:
        # Mock server info
        mock_server = Mock()
        mock_server.server_ip = "*************"
        mock_server.server_port = 8080
        mock_server.server_name = "Test Server"
        
        mock_manager.server_list = {"test_id": mock_server}
        
        # Test name resolution
        friendly_name = auth_ui._get_friendly_server_name("*************:8080")
        
        # Should fallback to IP since our mock doesn't match exactly
        assert "*************" in friendly_name, f"Expected IP in name, got {friendly_name}"
    
    print("✅ Friendly server names work")


def test_ui_manager_cleanup():
    """Test UI manager cleanup"""
    print("\n" + "="*70)
    print("🧪 Testing UI Manager Cleanup")
    print("="*70)
    
    auth_ui = AuthUIManager.get_instance()
    
    # Register some widgets
    mock_widget1 = Mock()
    mock_widget2 = Mock()
    
    auth_ui.register_server_status_widget("server1", mock_widget1)
    auth_ui.register_server_status_widget("server2", mock_widget2)
    
    # Verify widgets are registered
    assert len(auth_ui._server_status_widgets) == 2, "Widgets should be registered"
    
    # Cleanup
    auth_ui.cleanup()
    
    # Verify cleanup
    assert len(auth_ui._server_status_widgets) == 0, "Widgets should be cleared"
    assert not auth_ui._status_check_timer.isActive(), "Timer should be stopped"
    
    print("✅ UI manager cleanup works")


def test_token_expiration_ui_display():
    """Test token expiration information in UI"""
    print("\n" + "="*70)
    print("🧪 Testing Token Expiration UI Display")
    print("="*70)
    
    tm = SimpleTokenManager.get_instance()
    tm.clear_all_servers()
    
    server_id = "192.168.1.expire:8080"
    
    # Store token that expires soon
    tm.store_tokens(
        server_id=server_id,
        access_token="expire_token",
        refresh_token="expire_refresh",
        expires_in=300  # 5 minutes
    )
    
    # Get token info
    token_info = tm.get_token_info(server_id)
    
    assert token_info is not None, "Token info should exist"
    assert token_info.expires_soon(), "Token should expire soon"
    
    # Test time until expiry display
    time_left = token_info.time_until_expiry()
    assert time_left is not None, "Time left should be available"
    
    minutes = int(time_left.total_seconds() // 60)
    assert minutes <= 5, f"Should expire within 5 minutes, got {minutes}"
    
    print("✅ Token expiration UI display works")
    print(f"⏰ Token expires in {minutes} minutes")


def test_error_handling_in_ui():
    """Test error handling in UI components"""
    print("\n" + "="*70)
    print("🧪 Testing Error Handling in UI Components")
    print("="*70)
    
    auth_ui = AuthUIManager.get_instance()
    
    # Test with no notification parent
    auth_ui._notification_parent = None
    
    # These should not crash
    try:
        auth_ui.show_login_required_notification("test_server", "test reason")
        auth_ui.show_token_refresh_notification("test_server", True)
        auth_ui.show_server_connection_notification("test_server", False)
        auth_ui.show_authentication_error("test_server", "test error")
        
        print("✅ Error handling works - no crashes with missing parent")
        
    except Exception as e:
        print(f"❌ Error handling failed: {e}")
        raise
    
    # Test with invalid server IDs
    try:
        summary = auth_ui.get_server_status_summary()
        assert isinstance(summary, dict), "Should return dict even with no servers"
        
        print("✅ Error handling works - graceful handling of missing data")
        
    except Exception as e:
        print(f"❌ Error handling failed: {e}")
        raise


def main():
    """Run all Phase 4 UI tests"""
    print("🚀 Starting Phase 4 UI Integration Tests")
    
    try:
        test_auth_ui_manager_notifications()
        test_server_status_summary()
        test_ui_signal_emissions()
        test_server_status_widget_integration()
        test_friendly_server_names()
        test_ui_manager_cleanup()
        test_token_expiration_ui_display()
        test_error_handling_in_ui()
        
        print("\n" + "="*70)
        print("🎉 ALL PHASE 4 UI TESTS PASSED!")
        print("="*70)
        
        print("\n✅ Key Features Verified:")
        print("   📢 User-friendly notifications for auth events")
        print("   📊 Real-time server status display")
        print("   🔄 Token expiration information in UI")
        print("   📡 Signal-based UI updates")
        print("   🛡️ Error handling and graceful degradation")
        print("   🧹 Proper resource cleanup")
        
        print("\n🎯 Phase 4 Benefits:")
        print("   • Clear user feedback for authentication issues")
        print("   • Real-time server status visibility")
        print("   • Proactive token expiration warnings")
        print("   • Seamless integration with existing UI")
        print("   • Better user experience and debugging")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
