"""
Phase 2 Demo: Enhanced API Client Integration
Show how to solve token expiration check and proactive refresh
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

from src.common.auth.simple_token_manager import SimpleTokenManager, ServerAuthState

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class MockAPIClient:
    """Mock API Client showing Phase 2 integration"""
    
    def __init__(self, server_ip="*************", server_port=8080):
        self.server_ip = server_ip
        self.server_port = server_port
        self.access_token = None
        self.refresh_token = None
        self.is_ip_address = False
        
        # ✅ PHASE 2: Add token manager integration
        self.token_manager = SimpleTokenManager.get_instance()
        self.server_id = f"{self.server_ip}:{self.server_port}"
        
        logger.info(f"🔧 MockAPIClient created for server {self.server_id}")
    
    def login(self, username, password, expires_in=3600):
        """Mock login with token expiration"""
        logger.info(f"🔐 Logging in to {self.server_id} as {username}")
        
        # Simulate successful login
        self.access_token = f"access_token_for_{username}"
        self.refresh_token = f"refresh_token_for_{username}"
        
        # ✅ PHASE 2: Store tokens with expiration
        self.token_manager.store_tokens(
            server_id=self.server_id,
            access_token=self.access_token,
            refresh_token=self.refresh_token,
            server_url=f"http://{self.server_ip}:{self.server_port}",
            expires_in=expires_in
        )
        
        logger.info(f"✅ Login successful for {self.server_id}")
        return True
    
    def refresh_access_token(self):
        """Mock refresh method"""
        logger.info(f"🔄 Refreshing token for {self.server_id}")
        
        # Simulate refresh logic
        if "fail" in self.server_id:
            logger.warning(f"❌ Refresh failed for {self.server_id}")
            return None
        else:
            # Success
            self.access_token = f"new_access_token_{datetime.now().strftime('%H%M%S')}"
            logger.info(f"✅ Refresh successful for {self.server_id}")
            return self.access_token
    
    def _ensure_authenticated(self) -> bool:
        """
        ✅ SOLUTION 1: Check token expiration BEFORE API calls
        ✅ SOLUTION 2: Proactive refresh when token expires soon
        """
        if self.is_ip_address:
            return True
        
        # Check if server is connected
        if not self.token_manager.is_server_connected(self.server_id):
            logger.warning(f"⚠️ Server {self.server_id} is disconnected")
            return False
        
        # ✅ KEY METHOD: Ensure token is valid (proactive refresh if needed)
        if not self.token_manager.ensure_valid_token(self.server_id, self):
            logger.warning(f"❌ Failed to ensure valid token for {self.server_id}")
            return False
        
        return True
    
    def get_cameras(self):
        """Enhanced API call with proactive token management"""
        logger.info(f"📹 Getting cameras from {self.server_id}")
        
        # ✅ SOLUTION: Check authentication BEFORE API call
        if not self._ensure_authenticated():
            raise Exception(f"Server {self.server_id} disconnected. Please login again.")
        
        # Simulate API call
        logger.info(f"🌐 Making API call to get cameras...")
        
        # Simulate successful response
        return ["camera1", "camera2", "camera3"]
    
    def get_server_status(self) -> dict:
        """Get server authentication status"""
        token_info = self.token_manager.get_token_info(self.server_id)
        
        status = {
            "server_id": self.server_id,
            "connected": self.token_manager.is_server_connected(self.server_id),
            "state": self.token_manager.get_server_state(self.server_id).value,
            "error": self.token_manager.get_server_error(self.server_id)
        }
        
        if token_info:
            status["token_info"] = {
                "expires_at": token_info.expires_at.isoformat() if token_info.expires_at else None,
                "expires_soon": token_info.expires_soon(),
                "is_expired": token_info.is_expired(),
                "time_until_expiry": str(token_info.time_until_expiry()) if token_info.time_until_expiry() else None
            }
        
        return status


def demo_proactive_refresh():
    """Demo proactive refresh when token expires soon"""
    print("\n" + "="*70)
    print("🎬 DEMO: Proactive Refresh - Token Expires Soon")
    print("="*70)
    
    # Create client with token that expires in 4 minutes (will trigger proactive refresh)
    client = MockAPIClient("*************")
    client.login("admin", "password", expires_in=240)  # 4 minutes
    
    # Show token status
    status = client.get_server_status()
    token_info = status["token_info"]
    print(f"📊 Token Status:")
    print(f"   Expires at: {token_info['expires_at']}")
    print(f"   Expires soon: {token_info['expires_soon']}")
    print(f"   Time until expiry: {token_info['time_until_expiry']}")
    
    # Make API call - should trigger proactive refresh
    print(f"\n🔍 Making API call (should trigger proactive refresh)...")
    cameras = client.get_cameras()
    print(f"📹 Got cameras: {cameras}")
    
    # Show updated status
    status = client.get_server_status()
    print(f"📊 Updated Status: {status['state']}")


def demo_expired_token_handling():
    """Demo handling of expired tokens"""
    print("\n" + "="*70)
    print("🎬 DEMO: Expired Token Handling")
    print("="*70)
    
    # Create client and login
    client = MockAPIClient("*************")
    client.login("admin", "password", expires_in=3600)
    
    # Manually expire the token
    tm = SimpleTokenManager.get_instance()
    server_info = tm._servers[client.server_id]
    server_info.token.expires_at = datetime.now() - timedelta(minutes=10)  # 10 minutes ago
    
    print(f"🕐 Token manually expired 10 minutes ago")
    
    # Show token status
    status = client.get_server_status()
    token_info = status["token_info"]
    print(f"📊 Token Status:")
    print(f"   Is expired: {token_info['is_expired']}")
    print(f"   Time until expiry: {token_info['time_until_expiry']}")
    
    # Make API call - should trigger refresh
    print(f"\n🔍 Making API call (should trigger refresh for expired token)...")
    try:
        cameras = client.get_cameras()
        print(f"📹 Got cameras: {cameras}")
    except Exception as e:
        print(f"❌ API call failed: {e}")


def demo_refresh_failure_disconnect():
    """Demo disconnect when refresh fails"""
    print("\n" + "="*70)
    print("🎬 DEMO: Refresh Failure → Disconnect")
    print("="*70)
    
    # Create client that will fail refresh
    client = MockAPIClient("192.168.1.fail")  # "fail" in server_id triggers refresh failure
    client.login("admin", "password", expires_in=240)  # 4 minutes - will trigger refresh
    
    print(f"📊 Initial Status: {client.get_server_status()['state']}")
    
    # Make API call - should trigger refresh failure and disconnect
    print(f"\n🔍 Making API call (refresh will fail, server will disconnect)...")
    try:
        cameras = client.get_cameras()
        print(f"📹 Got cameras: {cameras}")
    except Exception as e:
        print(f"❌ API call failed: {e}")
    
    # Show final status
    final_status = client.get_server_status()
    print(f"📊 Final Status: {final_status['state']}")
    print(f"🚨 Error: {final_status['error']}")


def demo_multiple_servers():
    """Demo multiple server management with different token states"""
    print("\n" + "="*70)
    print("🎬 DEMO: Multiple Servers with Different Token States")
    print("="*70)
    
    # Create multiple clients
    clients = [
        ("Server A - Good token", MockAPIClient("*************")),
        ("Server B - Expires soon", MockAPIClient("*************")),
        ("Server C - Will fail refresh", MockAPIClient("192.168.1.fail"))
    ]
    
    # Login to all servers
    for description, client in clients:
        if "expires soon" in description.lower():
            client.login("admin", "password", expires_in=240)  # 4 minutes
        else:
            client.login("admin", "password", expires_in=3600)  # 1 hour
        print(f"✅ {description}: Logged in")
    
    # Show all server statuses
    print(f"\n📊 Server Statuses:")
    tm = SimpleTokenManager.get_instance()
    for server_id, server_info in tm.get_all_servers().items():
        status = "✅ Connected" if server_info.auth_state == ServerAuthState.CONNECTED else "❌ Disconnected"
        print(f"   {server_id}: {status}")
    
    # Make API calls to all servers
    print(f"\n🔍 Making API calls to all servers...")
    for description, client in clients:
        try:
            cameras = client.get_cameras()
            print(f"✅ {description}: Got {len(cameras)} cameras")
        except Exception as e:
            print(f"❌ {description}: {e}")
    
    # Show final statuses
    print(f"\n📊 Final Server Statuses:")
    for server_id, server_info in tm.get_all_servers().items():
        status = "✅ Connected" if server_info.auth_state == ServerAuthState.CONNECTED else "❌ Disconnected"
        error = f" ({server_info.last_error})" if server_info.last_error else ""
        print(f"   {server_id}: {status}{error}")


def demo_token_monitoring():
    """Demo token monitoring and debugging"""
    print("\n" + "="*70)
    print("🎬 DEMO: Token Monitoring and Debugging")
    print("="*70)
    
    # Create client with short-lived token
    client = MockAPIClient("192.168.1.monitor")
    client.login("admin", "password", expires_in=600)  # 10 minutes
    
    # Show detailed token information
    status = client.get_server_status()
    print(f"📊 Detailed Server Status:")
    print(f"   Server ID: {status['server_id']}")
    print(f"   Connected: {status['connected']}")
    print(f"   State: {status['state']}")
    
    if status.get('token_info'):
        token_info = status['token_info']
        print(f"   Token Info:")
        print(f"     Expires at: {token_info['expires_at']}")
        print(f"     Expires soon: {token_info['expires_soon']}")
        print(f"     Is expired: {token_info['is_expired']}")
        print(f"     Time until expiry: {token_info['time_until_expiry']}")
    
    # Show token manager overview
    tm = SimpleTokenManager.get_instance()
    print(f"\n📈 Token Manager Overview:")
    print(f"   Total servers: {len(tm.get_all_servers())}")
    print(f"   Connected servers: {len(tm.get_connected_servers())}")
    print(f"   Connected list: {tm.get_connected_servers()}")


def main():
    """Run all Phase 2 demos"""
    print("🚀 Phase 2 Demo: Enhanced API Client Integration")
    print("Solving: Token expiration check + Proactive refresh")
    
    try:
        demo_proactive_refresh()
        demo_expired_token_handling()
        demo_refresh_failure_disconnect()
        demo_multiple_servers()
        demo_token_monitoring()
        
        print("\n" + "="*70)
        print("🎉 ALL PHASE 2 DEMOS COMPLETED!")
        print("="*70)
        
        print("\n✅ Key Problems SOLVED:")
        print("   🔍 Token expiration check BEFORE API calls")
        print("   ⏰ Proactive refresh when token expires soon (5 min buffer)")
        print("   🔌 Graceful disconnect when refresh fails")
        print("   📊 Comprehensive server status monitoring")
        print("   🛡️ Better error handling and user feedback")
        
        print("\n🎯 Benefits:")
        print("   • No more failed API calls due to expired tokens")
        print("   • Seamless user experience with proactive refresh")
        print("   • Clear feedback when login is required")
        print("   • Better resource utilization (fewer failed requests)")
        
    except Exception as e:
        print(f"\n❌ DEMO FAILED: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
