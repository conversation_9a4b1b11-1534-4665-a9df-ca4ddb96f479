"""
Simple Token Manager for OAuth 2.0 - Lightweight Implementation
Focus: Disconnect when refresh_token expires, minimal code changes
"""

import threading
import logging
from dataclasses import dataclass
from enum import Enum
from typing import Dict, Optional
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class ServerAuthState(Enum):
    """Simple server authentication states"""
    CONNECTED = "connected"        # Có token, đang hoạt động
    DISCONNECTED = "disconnected"  # Không có token hoặc refresh failed
    REFRESHING = "refreshing"      # Đang thử refresh


@dataclass
class SimpleTokenInfo:
    """Lightweight token information"""
    access_token: str
    refresh_token: str
    server_id: str
    expires_at: Optional[datetime] = None
    created_at: datetime = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

    def is_valid(self) -> bool:
        """Check if tokens exist"""
        return bool(self.access_token and self.refresh_token)

    def is_expired(self) -> bool:
        """Check if token is expired"""
        if not self.expires_at:
            return False
        return datetime.now() >= self.expires_at

    def expires_soon(self, buffer_minutes: int = 5) -> bool:
        """Check if token expires within buffer time"""
        if not self.expires_at:
            return False
        buffer_time = timedelta(minutes=buffer_minutes)
        return datetime.now() >= (self.expires_at - buffer_time)

    def time_until_expiry(self) -> Optional[timedelta]:
        """Get time remaining until expiration"""
        if not self.expires_at:
            return None
        return self.expires_at - datetime.now()


@dataclass
class ServerInfo:
    """Server authentication information"""
    server_id: str
    server_url: str
    auth_state: ServerAuthState
    token: Optional[SimpleTokenInfo] = None
    last_error: Optional[str] = None


class SimpleTokenManager:
    """
    Lightweight token manager with focus on:
    - One token per server
    - Try refresh once, fail = disconnect
    - Clear user feedback when login needed
    - Camera state sync
    """
    
    _instance = None
    _lock = threading.Lock()
    
    @classmethod
    def get_instance(cls):
        """Singleton pattern - thread safe"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance
    
    def __init__(self):
        """Initialize token manager"""
        if SimpleTokenManager._instance is not None:
            raise Exception("Use get_instance() instead")
        
        self._servers: Dict[str, ServerInfo] = {}
        self._refresh_locks: Dict[str, threading.Lock] = {}
        logger.info("SimpleTokenManager initialized")
    
    def store_tokens(self, server_id: str, access_token: str, refresh_token: str,
                    server_url: str = None, expires_in: int = None):
        """Store tokens after successful login"""
        try:
            # Calculate expiration time if provided
            expires_at = None
            if expires_in:
                expires_at = datetime.now() + timedelta(seconds=expires_in)
                logger.debug(f"Token expires in {expires_in} seconds, at {expires_at}")

            token = SimpleTokenInfo(
                access_token=access_token,
                refresh_token=refresh_token,
                server_id=server_id,
                expires_at=expires_at
            )

            self._servers[server_id] = ServerInfo(
                server_id=server_id,
                server_url=server_url or server_id,
                auth_state=ServerAuthState.CONNECTED,
                token=token,
                last_error=None
            )

            logger.info(f"✅ Tokens stored for server {server_id}")
            if expires_at:
                logger.info(f"🕐 Token expires at: {expires_at}")

        except Exception as e:
            logger.error(f"❌ Failed to store tokens for server {server_id}: {e}")
    
    def get_access_token(self, server_id: str) -> Optional[str]:
        """Get current access token"""
        server_info = self._servers.get(server_id)
        if server_info and server_info.token and server_info.token.is_valid():
            return server_info.token.access_token
        return None
    
    def get_refresh_token(self, server_id: str) -> Optional[str]:
        """Get current refresh token"""
        server_info = self._servers.get(server_id)
        if server_info and server_info.token and server_info.token.is_valid():
            return server_info.token.refresh_token
        return None
    
    def is_server_connected(self, server_id: str) -> bool:
        """Check if server is connected (has valid tokens)"""
        server_info = self._servers.get(server_id)
        return (server_info and 
                server_info.auth_state == ServerAuthState.CONNECTED and
                server_info.token and 
                server_info.token.is_valid())
    
    def get_server_state(self, server_id: str) -> ServerAuthState:
        """Get current server authentication state"""
        server_info = self._servers.get(server_id)
        if server_info:
            return server_info.auth_state
        return ServerAuthState.DISCONNECTED
    
    def get_server_error(self, server_id: str) -> Optional[str]:
        """Get last error message for server"""
        server_info = self._servers.get(server_id)
        if server_info:
            return server_info.last_error
        return None

    def ensure_valid_token(self, server_id: str, api_client) -> bool:
        """
        Ensure token is valid before API calls - KEY METHOD for proactive refresh

        Args:
            server_id: Server identifier
            api_client: APIClient instance for refresh operations

        Returns:
            bool: True if token is valid, False if server disconnected
        """
        server_info = self._servers.get(server_id)
        if not server_info or not server_info.token:
            logger.warning(f"⚠️ No token for server {server_id}")
            return False

        token = server_info.token

        # Check if token is expired or expires soon
        if token.is_expired():
            logger.info(f"🕐 Token expired for server {server_id}, refreshing...")
            return self.try_refresh_token(server_id, api_client)

        elif token.expires_soon():
            logger.info(f"⏰ Token expires soon for server {server_id}, proactive refresh...")
            return self.try_refresh_token(server_id, api_client)

        # Token is still valid
        logger.debug(f"✅ Token valid for server {server_id}")
        return True

    def is_token_valid(self, server_id: str) -> bool:
        """Check if token exists and is not expired (without refresh)"""
        server_info = self._servers.get(server_id)
        if not server_info or not server_info.token:
            return False

        token = server_info.token
        return token.is_valid() and not token.is_expired()

    def get_token_info(self, server_id: str) -> Optional[SimpleTokenInfo]:
        """Get token info for debugging/monitoring"""
        server_info = self._servers.get(server_id)
        if server_info:
            return server_info.token
        return None
    
    def try_refresh_token(self, server_id: str, api_client) -> bool:
        """
        Try to refresh token once. If fail, disconnect server.
        
        Args:
            server_id: Server identifier
            api_client: APIClient instance to perform refresh
            
        Returns:
            bool: True if refresh successful, False if failed (server disconnected)
        """
        
        # Prevent concurrent refresh
        if server_id not in self._refresh_locks:
            self._refresh_locks[server_id] = threading.Lock()
            
        with self._refresh_locks[server_id]:
            server_info = self._servers.get(server_id)
            if not server_info or not server_info.token:
                logger.warning(f"⚠️ No token info for server {server_id}")
                return False
            
            # Set state to refreshing
            server_info.auth_state = ServerAuthState.REFRESHING
            logger.info(f"🔄 Attempting token refresh for server {server_id}")
            
            try:
                # Use existing APIClient refresh method
                new_access_token = api_client.refresh_access_token()
                
                if new_access_token:
                    # Success - update token
                    server_info.token.access_token = new_access_token
                    # Also update refresh token if APIClient provides it
                    if hasattr(api_client, 'refresh_token') and api_client.refresh_token:
                        server_info.token.refresh_token = api_client.refresh_token
                    
                    server_info.auth_state = ServerAuthState.CONNECTED
                    server_info.last_error = None
                    logger.info(f"✅ Token refresh successful for server {server_id}")
                    return True
                else:
                    # Refresh failed - disconnect
                    self._disconnect_server(server_id, "Refresh token expired or invalid")
                    return False
                    
            except Exception as e:
                # Any error during refresh - disconnect
                self._disconnect_server(server_id, f"Token refresh error: {str(e)}")
                return False
    
    def _disconnect_server(self, server_id: str, reason: str):
        """Disconnect server and clear tokens"""
        logger.warning(f"🔌 Disconnecting server {server_id}: {reason}")
        
        if server_id in self._servers:
            self._servers[server_id].auth_state = ServerAuthState.DISCONNECTED
            self._servers[server_id].token = None
            self._servers[server_id].last_error = reason
        
        # Notify UI that login is required
        self._notify_login_required(server_id, reason)
        
        # Set cameras offline (placeholder for now)
        self._set_cameras_offline(server_id)
    
    def disconnect_server(self, server_id: str, reason: str = "Manual disconnect"):
        """Manually disconnect server (e.g., user logout)"""
        self._disconnect_server(server_id, reason)
    
    def clear_all_servers(self):
        """Clear all server tokens (e.g., app shutdown)"""
        logger.info("🧹 Clearing all server tokens")
        self._servers.clear()
        self._refresh_locks.clear()
    
    def get_connected_servers(self) -> list:
        """Get list of connected server IDs"""
        return [
            server_id for server_id, server_info in self._servers.items()
            if server_info.auth_state == ServerAuthState.CONNECTED
        ]
    
    def get_all_servers(self) -> Dict[str, ServerInfo]:
        """Get all server information (for debugging/monitoring)"""
        return self._servers.copy()
    
    def _notify_login_required(self, server_id: str, reason: str):
        """Notify UI that user needs to login again"""
        logger.info(f"📢 Login required for server {server_id}: {reason}")
        
        # TODO: Integrate with existing notification system
        # This can be implemented later based on how UI notifications work
        notification_data = {
            "server_id": server_id,
            "message": f"Session expired for server {server_id}. Please login again.",
            "reason": reason,
            "action_required": "LOGIN_REQUIRED"
        }
        
        # For now, just log - actual implementation will depend on UI framework
        logger.info(f"📱 Notification: {notification_data}")
    
    def _set_cameras_offline(self, server_id: str):
        """Set all cameras for this server to offline state"""
        logger.info(f"📹 Setting cameras offline for server {server_id}")
        
        # TODO: Integrate with existing camera management
        # This will be implemented in Phase 4 when we integrate with UI
        
        # Example integration (to be implemented later):
        # camera_manager = CameraManager.get_instance()
        # cameras = camera_manager.get_cameras_by_server(server_id)
        # for camera in cameras:
        #     camera.set_state("offline")
        #     camera.set_auth_state("unauthenticated")
    
    def update_access_token(self, server_id: str, new_access_token: str):
        """Update access token after successful refresh"""
        server_info = self._servers.get(server_id)
        if server_info and server_info.token:
            server_info.token.access_token = new_access_token
            logger.debug(f"🔄 Updated access token for server {server_id}")
    
    def __str__(self):
        """String representation for debugging"""
        connected = len(self.get_connected_servers())
        total = len(self._servers)
        return f"SimpleTokenManager(connected={connected}, total={total})"
