"""
Enhanced Login Dialog with SimpleTokenManager Integration
Phase 4: UI Integration for authentication management
"""

import logging
from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QD<PERSON>og

from src.presentation.server_screen.login_dialog import LoginDialog
from src.common.auth.simple_token_manager import SimpleTokenManager
from src.common.auth.auth_ui_manager import AuthUIManager
from src.common.controller.controller_manager import controller_manager
from src.common.widget.notifications.notify import Notifications
from src.styles.style import Style

logger = logging.getLogger(__name__)


class EnhancedLoginDialog(LoginDialog):
    """
    Enhanced Login Dialog with token manager integration
    
    Key Features:
    1. ✅ Automatic token storage after successful login
    2. ✅ Server status updates
    3. ✅ Better error handling and user feedback
    4. ✅ Integration with AuthUIManager
    """
    
    # Additional signals for enhanced functionality
    login_successful = Signal(str)  # server_id
    login_failed = Signal(str, str)  # server_id, reason
    
    def __init__(self, parent=None, controller=None):
        super().__init__(parent, controller)
        
        # Add token manager integration
        self.token_manager = SimpleTokenManager.get_instance()
        self.auth_ui_manager = AuthUIManager.get_instance()
        self.server_id = self._generate_server_id()
        
        # Setup UI enhancements
        self._setup_enhanced_ui()
        
        logger.info(f"🔐 EnhancedLoginDialog created for server {self.server_id}")
    
    def _generate_server_id(self) -> str:
        """Generate server ID consistent with APIClient"""
        if self.controller and self.controller.server:
            server_ip = self.controller.server.data.server_ip
            server_port = self.controller.server.data.server_port
            return f"{server_ip}:{server_port}"
        return "unknown_server"
    
    def _setup_enhanced_ui(self):
        """Setup enhanced UI elements"""
        try:
            # Add server status indicator
            self._add_server_status_indicator()
            
            # Add token status information
            self._add_token_status_info()
            
            # Connect enhanced signals
            self.login_successful.connect(self._on_login_successful)
            self.login_failed.connect(self._on_login_failed)
            
        except Exception as e:
            logger.error(f"❌ Failed to setup enhanced UI: {e}")
    
    def _add_server_status_indicator(self):
        """Add server connection status indicator"""
        try:
            # Create status label
            self.status_label = QLabel()
            self.status_label.setStyleSheet("""
                QLabel {
                    font-size: 12px;
                    padding: 5px;
                    border-radius: 3px;
                    margin: 5px 0px;
                }
            """)
            
            # Add to layout (insert before login button)
            layout = self.layout()
            if layout:
                # Find login button and insert before it
                for i in range(layout.count()):
                    item = layout.itemAt(i)
                    if item and item.widget() and isinstance(item.widget(), QPushButton):
                        if "login" in item.widget().text().lower():
                            layout.insertWidget(i, self.status_label)
                            break
            
            # Update status
            self._update_server_status_indicator()
            
        except Exception as e:
            logger.error(f"❌ Failed to add status indicator: {e}")
    
    def _add_token_status_info(self):
        """Add token status information"""
        try:
            # Check if server has existing tokens
            if self.token_manager.is_server_connected(self.server_id):
                token_info = self.token_manager.get_token_info(self.server_id)
                if token_info:
                    # Show token expiration info
                    self.token_info_label = QLabel()
                    self.token_info_label.setStyleSheet("""
                        QLabel {
                            font-size: 11px;
                            color: #666;
                            padding: 3px;
                            margin: 2px 0px;
                        }
                    """)
                    
                    if token_info.expires_at:
                        time_left = token_info.time_until_expiry()
                        if time_left:
                            hours = int(time_left.total_seconds() // 3600)
                            minutes = int((time_left.total_seconds() % 3600) // 60)
                            self.token_info_label.setText(f"Current session expires in {hours}h {minutes}m")
                        else:
                            self.token_info_label.setText("Session expired")
                    else:
                        self.token_info_label.setText("Active session (no expiration info)")
                    
                    # Add to layout
                    layout = self.layout()
                    if layout:
                        layout.insertWidget(0, self.token_info_label)
            
        except Exception as e:
            logger.error(f"❌ Failed to add token status info: {e}")
    
    def _update_server_status_indicator(self):
        """Update server status indicator"""
        try:
            if not hasattr(self, 'status_label'):
                return
            
            if self.token_manager.is_server_connected(self.server_id):
                self.status_label.setText("🟢 Server Connected")
                self.status_label.setStyleSheet(self.status_label.styleSheet() + """
                    QLabel { background-color: #d4edda; color: #155724; }
                """)
            else:
                server_error = self.token_manager.get_server_error(self.server_id)
                if server_error:
                    self.status_label.setText(f"🔴 Disconnected: {server_error}")
                else:
                    self.status_label.setText("🔴 Server Disconnected")
                
                self.status_label.setStyleSheet(self.status_label.styleSheet() + """
                    QLabel { background-color: #f8d7da; color: #721c24; }
                """)
            
        except Exception as e:
            logger.error(f"❌ Failed to update status indicator: {e}")
    
    def btn_login_clicked(self):
        """Enhanced login with token manager integration"""
        try:
            logger.info(f"🔐 Enhanced login attempt for server {self.server_id}")
            
            # Show loading state
            self._set_loading_state(True)
            
            # Call parent login method
            super().btn_login_clicked()
            
            # The actual login result will be handled in the response
            
        except Exception as e:
            logger.error(f"❌ Enhanced login error: {e}")
            self._set_loading_state(False)
            self.login_failed.emit(self.server_id, str(e))
    
    def _set_loading_state(self, loading: bool):
        """Set UI loading state"""
        try:
            if hasattr(self, 'btn_login'):
                if loading:
                    self.btn_login.setText("Connecting...")
                    self.btn_login.setEnabled(False)
                else:
                    self.btn_login.setText("Login")
                    self.btn_login.setEnabled(True)
            
        except Exception as e:
            logger.error(f"❌ Failed to set loading state: {e}")
    
    def handle_login_response(self, response, deny_permission=False):
        """Enhanced login response handling"""
        try:
            # Call parent response handling first
            success = self._process_login_response(response, deny_permission)
            
            if success:
                # Login successful - store tokens in token manager
                self._handle_successful_login(response)
                self.login_successful.emit(self.server_id)
            else:
                # Login failed
                reason = "Permission denied" if deny_permission else "Invalid credentials"
                self._handle_failed_login(reason)
                self.login_failed.emit(self.server_id, reason)
            
            # Reset loading state
            self._set_loading_state(False)
            
        except Exception as e:
            logger.error(f"❌ Error handling login response: {e}")
            self._set_loading_state(False)
            self.login_failed.emit(self.server_id, str(e))
    
    def _process_login_response(self, response, deny_permission=False):
        """Process login response and determine success"""
        try:
            if response and response.status_code == 200:
                # Check if this is an IP server (no token needed)
                if self.controller and hasattr(self.controller, 'is_ip_address'):
                    if self.controller.is_ip_address:
                        return True
                
                # Check for access token in response
                if hasattr(self.controller, 'access_token') and self.controller.access_token:
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Error processing login response: {e}")
            return False
    
    def _handle_successful_login(self, response):
        """Handle successful login"""
        try:
            logger.info(f"✅ Login successful for server {self.server_id}")
            
            # Store tokens in token manager if available
            if (hasattr(self.controller, 'access_token') and 
                hasattr(self.controller, 'refresh_token') and
                self.controller.access_token and 
                self.controller.refresh_token):
                
                # Extract expires_in from response if available
                expires_in = None
                try:
                    if hasattr(response, 'json'):
                        json_data = response.json()
                        data = json_data.get("data", {})
                        expires_in = data.get("expires_in") or data.get("expiresIn")
                except:
                    pass
                
                # Store tokens
                self.token_manager.store_tokens(
                    server_id=self.server_id,
                    access_token=self.controller.access_token,
                    refresh_token=self.controller.refresh_token,
                    server_url=self.controller.server_url if hasattr(self.controller, 'server_url') else None,
                    expires_in=expires_in
                )
                
                logger.info(f"🔑 Tokens stored for server {self.server_id}")
            
            # Show success notification
            self.auth_ui_manager.show_server_connection_notification(
                self.server_id, True, "Login successful"
            )
            
            # Update status indicator
            self._update_server_status_indicator()
            
        except Exception as e:
            logger.error(f"❌ Error handling successful login: {e}")
    
    def _handle_failed_login(self, reason: str):
        """Handle failed login"""
        try:
            logger.warning(f"❌ Login failed for server {self.server_id}: {reason}")
            
            # Show error notification
            self.auth_ui_manager.show_authentication_error(self.server_id, reason)
            
            # Update status indicator
            self._update_server_status_indicator()
            
        except Exception as e:
            logger.error(f"❌ Error handling failed login: {e}")
    
    def _on_login_successful(self, server_id: str):
        """Handle login successful signal"""
        try:
            logger.info(f"🎉 Login successful signal received for {server_id}")
            
            # Enable any WebSocket reconnection
            self._enable_websocket_reconnection()
            
            # Update UI components
            self.auth_ui_manager.force_refresh_ui()
            
        except Exception as e:
            logger.error(f"❌ Error handling login successful signal: {e}")
    
    def _on_login_failed(self, server_id: str, reason: str):
        """Handle login failed signal"""
        try:
            logger.warning(f"❌ Login failed signal received for {server_id}: {reason}")
            
            # Could show additional UI feedback here
            
        except Exception as e:
            logger.error(f"❌ Error handling login failed signal: {e}")
    
    def _enable_websocket_reconnection(self):
        """Enable WebSocket reconnection after successful login"""
        try:
            # Find and enable WebSocket reconnection
            # This would integrate with EnhancedWebsocketClient
            # For now, just log the intent
            logger.info(f"🌐 Enabling WebSocket reconnection for {self.server_id}")
            
        except Exception as e:
            logger.error(f"❌ Error enabling WebSocket reconnection: {e}")
    
    def closeEvent(self, event):
        """Handle dialog close event"""
        try:
            # Cleanup any resources
            logger.info(f"🔐 Enhanced login dialog closing for {self.server_id}")
            super().closeEvent(event)
            
        except Exception as e:
            logger.error(f"❌ Error during close event: {e}")
            super().closeEvent(event)
