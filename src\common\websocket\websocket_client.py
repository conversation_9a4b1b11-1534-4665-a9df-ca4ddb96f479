from abc import abstractmethod
from http import HTTPStatus
import json
import time
from typing import Callable, Generic, List, Optional, TypeVar
import websocket
import threading
from websocket._exceptions import WebSocketBadStatusException
from .event_type import EventType
from .message_processor import MessageProcessor
from src.common.controller.controller_manager import Controller, controller_manager
from src.common.qml.models.map_controller import map_manager, MapModel
from src.common.auth.auth_manager import auth_manager
from PySide6.QtCore import QObject, Signal
import queue
import logging

logger = logging.getLogger(__name__)

THandler = TypeVar('THandler', bound=object)

class WebsocketClient(Generic[THandler]):

    ws: websocket.WebSocketApp
    url: str
    event_callback: Optional[Callable[[dict], None]]
    header: dict
    handlers: List[THandler]
    server_ip: str

    def __init__(self, url: str, header: Optional[dict] = None, 
                 server_ip: Optional[str] = None):
        self.url = url

        self.header = header or {}
        self.server_ip = server_ip
        self.handlers = []

        self.ws = websocket.WebSocketApp(
            url=self.url,
            on_open=self.on_open,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close,
            header=self.header
        )
        self.messageProcessor = MessageProcessor(server_ip=server_ip)
        
    def connect_background(self):
        self.thread = threading.Thread(target=self.connect)
        self.thread.daemon = True
        self.thread.start()

    def connect(self):
        self.ws.run_forever()

    def set_url(self, url: str):
        self.url = url
        self.ws.url = url

    def subscribe(self, handler: THandler):
        self.handlers.append(handler)

    def send(self, data):
        self.ws.send(data)

    def close(self):
        self.ws.close()

    def on_open(self, ws):
        data = {"message": "You are connected"}
        event = EventType.connection_established
        message_json = {'event': event, 'data': data}
        self.__on_websocket_event(message_json)
        logger.info("Connected")

    def on_message(self, ws, message):
        try:
            message_json = json.loads(message)
            # print(f"[WebSocket] Received event: {message_json}")  # Log event
            # xử lý tính toán dữ liệu lâu
            self.messageProcessor.message_queue.put(message_json)
            # self.__on_websocket_event(message_json)
        except Exception as e:
            logger.exception("Error in on_message", exc_info=True)

    def on_error(self, ws, error: Exception):
        message = f"WebSocket error: {str(error)}"
        data = {"message": message, "error": str(error)}
        event = EventType.connection_error
        message_json = {'event': event, 'data': data}
        self.__on_websocket_event(message_json)

        if isinstance(error, WebSocketBadStatusException):
            if error.status_code == HTTPStatus.UNAUTHORIZED:
                print(f"🚫 [WEBSOCKET] Access token expired for server: {self.server_ip}")
                logger.warning(f"🚫 [WEBSOCKET] Access token expired for server: {self.server_ip}")

                # Notify AuthManager about token expiration
                auth_manager.handle_token_expired(self.server_ip, "WebSocket connection")

                ok = self.refresh_token()
                if not ok:
                    print(f"❌ [WEBSOCKET] Cannot refresh token - Server disconnected: {self.server_ip}")
                    logger.error(f"❌ [WEBSOCKET] Cannot refresh token - Server disconnected: {self.server_ip}")
                    print(f"🔐 [AUTH] Please login again to server: {self.server_ip}")
                    logger.error(f"🔐 [AUTH] Please login again to server: {self.server_ip}")
                    self.header = {'Id': '123'}
                    self._notify_server_disconnected("Token refresh failed")
                else:
                    print(f"✅ [WEBSOCKET] Token refreshed successfully for server: {self.server_ip}")
                    logger.info(f"✅ [WEBSOCKET] Token refreshed successfully for server: {self.server_ip}")
                    
    def refresh_token(self) -> bool:
        try:
            controller: Controller = controller_manager.get_controller(server_ip=self.server_ip)
            if not controller:
                print(f"❌ [WEBSOCKET] No controller found for server: {self.server_ip}")
                logger.error(f"❌ [WEBSOCKET] No controller found for server: {self.server_ip}")
                return False

            access_token = controller.refresh_access_token()
            if access_token:
                self.header['Authorization'] = f"Bearer {access_token}"
                map_model: MapModel = map_manager.get_map_model(serverIp=self.server_ip)
                if map_model:
                    map_model.accessTokenChanged.emit()
                print(f"✅ [WEBSOCKET] Token refreshed and header updated for server: {self.server_ip}")
                logger.info(f"✅ [WEBSOCKET] Token refreshed and header updated for server: {self.server_ip}")
                return True
            else:
                print(f"❌ [WEBSOCKET] Token refresh failed for server: {self.server_ip}")
                logger.error(f"❌ [WEBSOCKET] Token refresh failed for server: {self.server_ip}")
                return False
        except Exception as e:
            print(f"❌ [WEBSOCKET] Token refresh error for server {self.server_ip}: {e}")
            logger.error(f"❌ [WEBSOCKET] Token refresh error for server {self.server_ip}: {e}")
            return False

    def _notify_server_disconnected(self, reason: str):
        """Notify about server disconnection due to authentication failure"""
        try:
            print(f"🔌 [WEBSOCKET] Server disconnected: {self.server_ip} - Reason: {reason}")
            logger.warning(f"🔌 [WEBSOCKET] Server disconnected: {self.server_ip} - Reason: {reason}")

            # TODO: Implement notification to server_screen
            # This could be done through:
            # 1. Signal emission to UI components
            # 2. Event system notification
            # 3. Controller manager notification

            # For now, just emit a websocket event that can be handled by UI
            data = {
                "server_ip": self.server_ip,
                "reason": reason,
                "action_required": "LOGIN_REQUIRED"
            }
            event = EventType.connection_error
            message_json = {'event': event, 'data': data}
            self.__on_websocket_event(message_json)

        except Exception as e:
            logger.error(f"Error notifying server disconnection: {e}")

    def on_close(self, ws, close_status_code, close_msg):
        message = f"close_status_code = {close_status_code} close_msg = {close_msg}"
        data = {"message": message}
        event = EventType.connection_lost
        message_json = {'event': event, 'data': data}
        self.__on_websocket_event(message_json)
        logger.info(f"Trying reconnect {close_status_code} {close_msg}")
        time.sleep(1)
        self.connect_background()

    def __on_websocket_event(self, message_json: dict):
        try:
            event = message_json['event']
            for handler in self.handlers:
                if event in handler.event_types:
                    handler.enqueue(message=message_json)
        except Exception:
            logger.exception("on_websocket_event exception error", exc_info=True)

