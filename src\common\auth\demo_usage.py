"""
Demo: How to use SimpleTokenManager in practice
This shows the integration pattern for APIClient and WebSocketClient
"""

import sys
import os
import logging

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from src.common.auth.simple_token_manager import SimpleTokenManager, ServerAuthState

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class MockAPIClient:
    """Mock APIClient showing integration pattern"""
    
    def __init__(self, server_ip="*************", server_port=8080):
        self.server_ip = server_ip
        self.server_port = server_port
        self.access_token = None
        self.refresh_token = None
        
        # ADD: Token manager integration
        self.token_manager = SimpleTokenManager.get_instance()
        self.server_id = f"{self.server_ip}:{self.server_port}"
        
        logger.info(f"🔧 APIClient created for server {self.server_id}")
    
    def login(self, username, password):
        """Mock login method"""
        logger.info(f"🔐 Logging in to {self.server_id} as {username}")
        
        # Simulate successful login
        self.access_token = f"access_token_for_{username}"
        self.refresh_token = f"refresh_token_for_{username}"
        
        # Store tokens in token manager
        self.token_manager.store_tokens(
            server_id=self.server_id,
            access_token=self.access_token,
            refresh_token=self.refresh_token,
            server_url=f"http://{self.server_ip}:{self.server_port}"
        )
        
        logger.info(f"✅ Login successful for {self.server_id}")
        return True
    
    def refresh_access_token(self):
        """Mock refresh method - sometimes fails"""
        logger.info(f"🔄 Refreshing token for {self.server_id}")
        
        # Simulate refresh logic - sometimes fails
        if "fail" in self.server_id:
            logger.warning(f"❌ Refresh failed for {self.server_id}")
            return None
        else:
            # Success
            self.access_token = f"new_access_token_{self.server_id}"
            logger.info(f"✅ Refresh successful for {self.server_id}")
            return self.access_token
    
    def get_cameras(self):
        """Mock API call that might trigger token refresh"""
        logger.info(f"📹 Getting cameras from {self.server_id}")
        
        # Check if server is connected
        if not self.token_manager.is_server_connected(self.server_id):
            raise Exception("Server disconnected. Please login again.")
        
        # Simulate 401 error sometimes
        if "unauthorized" in self.server_id:
            logger.warning(f"🚫 Got 401 Unauthorized from {self.server_id}")
            
            # Try refresh once
            if self.token_manager.try_refresh_token(self.server_id, self):
                logger.info(f"🔄 Token refreshed, retrying API call")
                # Retry the API call
                return ["camera1", "camera2", "camera3"]
            else:
                logger.error(f"❌ Refresh failed, server disconnected")
                raise Exception("Authentication failed. Please login again.")
        
        # Normal successful response
        return ["camera1", "camera2", "camera3"]


class MockWebSocketClient:
    """Mock WebSocketClient showing integration pattern"""
    
    def __init__(self, server_ip="*************"):
        self.server_ip = server_ip
        self.server_id = f"{server_ip}:8080"
        self.connected = False
        
        # ADD: Token manager integration
        self.token_manager = SimpleTokenManager.get_instance()
        
        logger.info(f"🌐 WebSocket client created for {self.server_id}")
    
    def connect(self):
        """Mock connect method"""
        if self.token_manager.is_server_connected(self.server_id):
            self.connected = True
            logger.info(f"🔗 WebSocket connected to {self.server_id}")
        else:
            logger.warning(f"⚠️ Cannot connect WebSocket - server {self.server_id} not authenticated")
    
    def on_error_401(self):
        """Mock 401 error handler"""
        logger.warning(f"🚫 WebSocket got 401 error from {self.server_id}")
        
        # Try refresh once
        if self.token_manager.is_server_connected(self.server_id):
            # Get APIClient for this server (in real code, this would be injected)
            api_client = MockAPIClient(self.server_ip.split(':')[0])
            
            if self.token_manager.try_refresh_token(self.server_id, api_client):
                logger.info(f"🔄 Token refreshed, WebSocket will reconnect")
                self.connect()  # Reconnect with new token
            else:
                logger.warning(f"❌ Refresh failed, WebSocket disconnected")
                self.connected = False
        else:
            logger.warning(f"⚠️ Server disconnected, WebSocket will not reconnect")
            self.connected = False
    
    def on_close(self):
        """Mock close handler"""
        logger.info(f"🔌 WebSocket connection closed for {self.server_id}")
        
        # Only reconnect if server is still connected
        if self.token_manager.is_server_connected(self.server_id):
            logger.info(f"🔄 Server connected, attempting WebSocket reconnect")
            self.connect()
        else:
            logger.info(f"⚠️ Server disconnected, not reconnecting WebSocket")
            self.connected = False


def demo_successful_flow():
    """Demo successful authentication and API usage"""
    print("\n" + "="*60)
    print("🎬 DEMO: Successful Authentication Flow")
    print("="*60)
    
    # Create clients
    api_client = MockAPIClient("*************")
    ws_client = MockWebSocketClient("*************")
    
    # Login
    api_client.login("admin", "password123")
    
    # Connect WebSocket
    ws_client.connect()
    
    # Make API calls
    cameras = api_client.get_cameras()
    logger.info(f"📹 Got cameras: {cameras}")
    
    # Check status
    tm = SimpleTokenManager.get_instance()
    logger.info(f"📊 Server status: {tm.get_server_state(api_client.server_id)}")


def demo_token_refresh_flow():
    """Demo token refresh when getting 401"""
    print("\n" + "="*60)
    print("🎬 DEMO: Token Refresh Flow")
    print("="*60)
    
    # Create client that will get 401
    api_client = MockAPIClient("192.168.1.unauthorized")
    ws_client = MockWebSocketClient("192.168.1.unauthorized")
    
    # Login first
    api_client.login("admin", "password123")
    ws_client.connect()
    
    # Make API call that triggers 401 and refresh
    try:
        cameras = api_client.get_cameras()
        logger.info(f"📹 Got cameras after refresh: {cameras}")
    except Exception as e:
        logger.error(f"❌ API call failed: {e}")
    
    # Simulate WebSocket 401
    ws_client.on_error_401()


def demo_refresh_failure_flow():
    """Demo what happens when refresh fails"""
    print("\n" + "="*60)
    print("🎬 DEMO: Refresh Failure -> Disconnect")
    print("="*60)
    
    # Create client that will fail refresh
    api_client = MockAPIClient("192.168.1.fail")
    ws_client = MockWebSocketClient("192.168.1.fail")
    
    # Login first
    api_client.login("admin", "password123")
    ws_client.connect()
    
    # Try API call that will trigger failed refresh
    try:
        cameras = api_client.get_cameras()
        logger.info(f"📹 Got cameras: {cameras}")
    except Exception as e:
        logger.error(f"❌ API call failed: {e}")
    
    # Check server status after failed refresh
    tm = SimpleTokenManager.get_instance()
    server_id = api_client.server_id
    logger.info(f"📊 Server status: {tm.get_server_state(server_id)}")
    logger.info(f"🚨 Error: {tm.get_server_error(server_id)}")
    
    # Try WebSocket operations
    ws_client.on_close()  # Should not reconnect


def demo_multi_server():
    """Demo multiple server management"""
    print("\n" + "="*60)
    print("🎬 DEMO: Multiple Server Management")
    print("="*60)
    
    # Create multiple servers
    servers = [
        ("*************", "Server A - Good"),
        ("*************", "Server B - Good"), 
        ("192.168.1.fail", "Server C - Will fail refresh")
    ]
    
    clients = []
    for server_ip, description in servers:
        logger.info(f"🔧 Setting up {description}")
        api_client = MockAPIClient(server_ip)
        api_client.login("admin", "password123")
        clients.append((api_client, description))
    
    # Show all connected
    tm = SimpleTokenManager.get_instance()
    logger.info(f"📊 Connected servers: {tm.get_connected_servers()}")
    
    # Trigger refresh failure on one server
    logger.info(f"🧪 Triggering refresh failure on Server C")
    try:
        clients[2][0].get_cameras()  # This will fail
    except:
        pass
    
    # Show final status
    logger.info(f"📊 Final connected servers: {tm.get_connected_servers()}")
    
    for server_id, server_info in tm.get_all_servers().items():
        status = "✅ Connected" if server_info.auth_state == ServerAuthState.CONNECTED else "❌ Disconnected"
        logger.info(f"📋 {server_id}: {status}")


def main():
    """Run all demos"""
    print("🚀 SimpleTokenManager Usage Demo")
    
    try:
        demo_successful_flow()
        demo_token_refresh_flow()
        demo_refresh_failure_flow()
        demo_multi_server()
        
        print("\n" + "="*60)
        print("🎉 ALL DEMOS COMPLETED!")
        print("="*60)
        
        # Show final state
        tm = SimpleTokenManager.get_instance()
        print(f"\n📊 Final Token Manager State:")
        print(f"   Total servers: {len(tm.get_all_servers())}")
        print(f"   Connected: {len(tm.get_connected_servers())}")
        print(f"   Connected servers: {tm.get_connected_servers()}")
        
    except Exception as e:
        print(f"\n❌ DEMO FAILED: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
