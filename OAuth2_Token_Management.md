# OAuth 2.0 Token Management - Lightweight Implementation

## 1. Tổng quan - Approach Nhẹ Nhàng

### 1.1 Core Focus: Disconnect When Refresh Token Expires
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Login Flow    │───▶│  Token Refresh   │───▶│ Graceful Logout │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Per-Server      │    │ Refresh Failed   │    │ Camera Offline  │
│ Token Store     │    │ → Disconnect     │    │ → Show Login    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### 1.2 Simple Principles
- **One Token Per Server**: Mỗi server có token riêng
- **Try Refresh Once**: Thử refresh 1 lần, fail thì disconnect
- **Clear User Feedback**: Thông báo rõ ràng khi cần login lại
- **Camera State Sync**: Camera offline khi server disconnect

## 2. Simple Token Structure

### 2.1 Lightweight TokenInfo
```python
@dataclass
class SimpleTokenInfo:
    access_token: str
    refresh_token: str
    server_id: str
    expires_at: Optional[datetime] = None  # Optional for now

    def is_valid(self) -> bool:
        """Check if tokens exist"""
        return bool(self.access_token and self.refresh_token)
```

### 2.2 Simple Server State
```python
class ServerAuthState(Enum):
    CONNECTED = "connected"        # Có token, đang hoạt động
    DISCONNECTED = "disconnected"  # Không có token hoặc refresh failed
    REFRESHING = "refreshing"      # Đang thử refresh

@dataclass
class ServerInfo:
    server_id: str
    server_url: str
    auth_state: ServerAuthState
    token: Optional[SimpleTokenInfo] = None
    last_error: Optional[str] = None
```

## 3. Simple Token Manager

### 3.1 Lightweight Token Manager
```python
class SimpleTokenManager:
    def __init__(self):
        self._servers: Dict[str, ServerInfo] = {}
        self._refresh_locks: Dict[str, threading.Lock] = {}

    def store_tokens(self, server_id: str, access_token: str, refresh_token: str):
        """Store tokens after successful login"""
        token = SimpleTokenInfo(
            access_token=access_token,
            refresh_token=refresh_token,
            server_id=server_id
        )

        self._servers[server_id] = ServerInfo(
            server_id=server_id,
            server_url=self._get_server_url(server_id),
            auth_state=ServerAuthState.CONNECTED,
            token=token
        )

        logger.info(f"Tokens stored for server {server_id}")

    def get_access_token(self, server_id: str) -> Optional[str]:
        """Get current access token"""
        server_info = self._servers.get(server_id)
        if server_info and server_info.token and server_info.token.is_valid():
            return server_info.token.access_token
        return None

    def is_server_connected(self, server_id: str) -> bool:
        """Check if server is connected (has valid tokens)"""
        server_info = self._servers.get(server_id)
        return (server_info and
                server_info.auth_state == ServerAuthState.CONNECTED and
                server_info.token and
                server_info.token.is_valid())
```

### 3.2 Simple Token Refresh - Try Once, Fail = Disconnect
```python
    def try_refresh_token(self, server_id: str, api_client) -> bool:
        """Try to refresh token once. If fail, disconnect server."""

        # Prevent concurrent refresh
        if server_id not in self._refresh_locks:
            self._refresh_locks[server_id] = threading.Lock()

        with self._refresh_locks[server_id]:
            server_info = self._servers.get(server_id)
            if not server_info or not server_info.token:
                logger.warning(f"No token info for server {server_id}")
                return False

            # Set state to refreshing
            server_info.auth_state = ServerAuthState.REFRESHING
            logger.info(f"Attempting token refresh for server {server_id}")

            try:
                # Use existing APIClient refresh method
                new_access_token = api_client.refresh_access_token()

                if new_access_token:
                    # Success - update token
                    server_info.token.access_token = new_access_token
                    server_info.auth_state = ServerAuthState.CONNECTED
                    server_info.last_error = None
                    logger.info(f"Token refresh successful for server {server_id}")
                    return True
                else:
                    # Refresh failed - disconnect
                    self._disconnect_server(server_id, "Refresh token expired or invalid")
                    return False

            except Exception as e:
                # Any error during refresh - disconnect
                self._disconnect_server(server_id, f"Token refresh error: {str(e)}")
                return False

    def _disconnect_server(self, server_id: str, reason: str):
        """Disconnect server and clear tokens"""
        logger.warning(f"Disconnecting server {server_id}: {reason}")

        if server_id in self._servers:
            self._servers[server_id].auth_state = ServerAuthState.DISCONNECTED
            self._servers[server_id].token = None
            self._servers[server_id].last_error = reason

        # Notify UI that login is required
        self._notify_login_required(server_id, reason)

        # Set cameras offline
        self._set_cameras_offline(server_id)
```

### 3.3 Simple Notification & Camera Management
```python
    def _notify_login_required(self, server_id: str, reason: str):
        """Notify UI that user needs to login again"""
        logger.info(f"Login required for server {server_id}: {reason}")

        # Emit signal for UI (using existing pattern)
        # This can be integrated with existing event system
        notification_data = {
            "server_id": server_id,
            "message": f"Session expired for server {server_id}. Please login again.",
            "reason": reason,
            "action_required": "LOGIN_REQUIRED"
        }

        # Send to existing notification system
        self._emit_notification(notification_data)

    def _set_cameras_offline(self, server_id: str):
        """Set all cameras for this server to offline state"""
        logger.info(f"Setting cameras offline for server {server_id}")

        # This will integrate with existing camera management
        # For now, just log - actual implementation will depend on
        # how cameras are tracked per server

        # Example integration:
        # camera_manager = CameraManager.get_instance()
        # cameras = camera_manager.get_cameras_by_server(server_id)
        # for camera in cameras:
        #     camera.set_state("offline")
        #     camera.set_auth_state("unauthenticated")

    def _emit_notification(self, data: dict):
        """Emit notification to UI - integrate with existing event system"""
        # This will integrate with existing notification/event system
        # Could use Qt signals, event bus, or existing notification mechanism
        pass
```

## 4. Simple Integration với Existing Code

### 4.1 Enhanced APIClient - Minimal Changes
```python
class APIClient:
    def __init__(self, server: ServerInfoModel = None):
        # Existing code...
        self.access_token = None
        self.refresh_token = None
        # ... existing initialization

        # ADD: Simple token manager integration
        self.token_manager = SimpleTokenManager.get_instance()
        self.server_id = self._generate_server_id()

    def _generate_server_id(self) -> str:
        """Generate unique server ID"""
        return f"{self.server_ip}:{self.server_port}"

    def login(self, username, password, captcha=None, captcha_id=None):
        """Enhanced login with token manager integration"""
        # Existing login logic...
        response = self._perform_existing_login(username, password, captcha, captcha_id)

        if response.status_code == 200 and not self.is_ip_address:
            # Store tokens in token manager
            self.token_manager.store_tokens(
                server_id=self.server_id,
                access_token=self.access_token,
                refresh_token=self.refresh_token
            )

        return response

    def refresh_access_token(self):
        """Enhanced refresh with disconnect on failure"""
        # Try existing refresh logic
        result = self._perform_existing_refresh()

        if result is None:
            # Refresh failed - notify token manager to disconnect
            self.token_manager._disconnect_server(
                self.server_id,
                "Refresh token expired"
            )

        return result

    def _make_api_request(self, method: str, url: str, **kwargs):
        """Enhanced API request with token validation"""
        # Check if server is connected
        if not self.token_manager.is_server_connected(self.server_id):
            raise AuthenticationError("Server disconnected. Please login again.")

        # Make request with existing logic
        response = self._perform_existing_request(method, url, **kwargs)

        # Handle 401 responses
        if response.status_code == 401:
            # Try refresh once
            if self.token_manager.try_refresh_token(self.server_id, self):
                # Retry request with new token
                return self._perform_existing_request(method, url, **kwargs)
            else:
                # Refresh failed, server disconnected
                raise AuthenticationError("Authentication failed. Please login again.")

        return response
```

### 4.2 Enhanced WebSocketClient - Minimal Changes
```python
class WebsocketClient:
    def __init__(self, url: str, header: Optional[dict] = None, server_ip: Optional[str] = None):
        # Existing initialization...
        self.url = url
        self.header = header or {}
        self.server_ip = server_ip

        # ADD: Token manager integration
        self.token_manager = SimpleTokenManager.get_instance()
        self.server_id = f"{server_ip}:8080"  # Or get from config

        # Existing WebSocket setup...
        self.ws = websocket.WebSocketApp(...)

    def on_error(self, ws, error: Exception):
        """Enhanced error handling with simple disconnect"""
        # Existing error handling...
        message = f"WebSocket error: {str(error)}"
        data = {"message": message, "error": str(error)}
        event = EventType.connection_error
        message_json = {'event': event, 'data': data}
        self.__on_websocket_event(message_json)

        if isinstance(error, WebSocketBadStatusException):
            if error.status_code == HTTPStatus.UNAUTHORIZED:
                # Try refresh once
                if self.token_manager.is_server_connected(self.server_id):
                    # Get APIClient instance for this server
                    api_client = self._get_api_client_for_server()

                    if self.token_manager.try_refresh_token(self.server_id, api_client):
                        # Refresh successful - update header and reconnect
                        new_token = self.token_manager.get_access_token(self.server_id)
                        self.header['Authorization'] = f"Bearer {new_token}"
                        logger.info("Token refreshed, will reconnect WebSocket")
                    else:
                        # Refresh failed - server disconnected
                        logger.warning("Token refresh failed, server disconnected")
                        self.header = {'Id': '123'}  # Fallback header
                else:
                    # Server already disconnected
                    logger.warning("Server disconnected, cannot refresh token")
                    self.header = {'Id': '123'}

    def on_close(self, ws, close_status_code, close_msg):
        """Enhanced close handling - don't reconnect if server disconnected"""
        # Existing close handling...
        message = f"close_status_code = {close_status_code} close_msg = {close_msg}"
        data = {"message": message}
        event = EventType.connection_lost
        message_json = {'event': event, 'data': data}
        self.__on_websocket_event(message_json)

        # Only reconnect if server is still connected
        if self.token_manager.is_server_connected(self.server_id):
            logger.info(f"Server connected, trying reconnect {close_status_code} {close_msg}")
            time.sleep(1)
            self.connect_background()
        else:
            logger.info(f"Server disconnected, not reconnecting WebSocket")
```

## 5. Simple Error Classes

### 5.1 Basic Exception Handling
```python
class AuthenticationError(Exception):
    """Authentication failed - need to login again"""
    pass

class ServerDisconnectedError(Exception):
    """Server is disconnected due to token issues"""
    pass
```

## 6. Implementation Steps - Nhẹ Nhàng

### 6.1 Step 1: Add SimpleTokenManager
```python
# Create new file: src/common/auth/simple_token_manager.py
class SimpleTokenManager:
    _instance = None

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def __init__(self):
        if SimpleTokenManager._instance is not None:
            raise Exception("Use get_instance()")
        self._servers: Dict[str, ServerInfo] = {}
        self._refresh_locks: Dict[str, threading.Lock] = {}

    # ... methods from above
```

### 6.2 Step 2: Minimal APIClient Changes
```python
# In existing api_client.py - just add a few lines
class APIClient:
    def __init__(self, server: ServerInfoModel = None):
        # ... existing code ...

        # ADD these lines:
        from src.common.auth.simple_token_manager import SimpleTokenManager
        self.token_manager = SimpleTokenManager.get_instance()
        self.server_id = f"{self.server_ip}:{self.server_port}"

    def login(self, username, password, captcha=None, captcha_id=None):
        # ... existing login code ...

        # ADD after successful login:
        if response.status_code == 200 and not self.is_ip_address:
            self.token_manager.store_tokens(
                self.server_id, self.access_token, self.refresh_token
            )

        return response

    def refresh_access_token(self):
        # ... existing refresh code ...

        # ADD after refresh attempt:
        if result is None:  # Refresh failed
            self.token_manager._disconnect_server(
                self.server_id, "Refresh token expired"
            )

        return result
```

### 6.3 Step 3: Minimal WebSocketClient Changes
```python
# In existing websocket_client.py - just modify a few methods
class WebsocketClient:
    def __init__(self, url: str, header: Optional[dict] = None, server_ip: Optional[str] = None):
        # ... existing code ...

        # ADD these lines:
        from src.common.auth.simple_token_manager import SimpleTokenManager
        self.token_manager = SimpleTokenManager.get_instance()
        self.server_id = f"{server_ip}:8080"  # Or get from config

    def on_error(self, ws, error: Exception):
        # ... existing error handling ...

        if isinstance(error, WebSocketBadStatusException):
            if error.status_code == HTTPStatus.UNAUTHORIZED:
                # REPLACE existing refresh logic with:
                if self.token_manager.is_server_connected(self.server_id):
                    # Server still connected, try refresh
                    controller = controller_manager.get_controller(server_ip=self.server_ip)
                    if controller and self.token_manager.try_refresh_token(self.server_id, controller):
                        # Success - update header
                        new_token = self.token_manager.get_access_token(self.server_id)
                        self.header['Authorization'] = f"Bearer {new_token}"
                    else:
                        # Failed - server disconnected
                        self.header = {'Id': '123'}
                else:
                    # Server already disconnected
                    self.header = {'Id': '123'}

    def on_close(self, ws, close_status_code, close_msg):
        # ... existing close handling ...

        # MODIFY reconnect logic:
        if self.token_manager.is_server_connected(self.server_id):
            logger.info(f"Trying reconnect {close_status_code} {close_msg}")
            time.sleep(1)
            self.connect_background()
        else:
            logger.info("Server disconnected, not reconnecting")
```

### 6.4 Step 4: Camera State Integration (Optional)
```python
# In existing camera_model.py - add simple auth state tracking
class CameraModel:
    def __init__(self):
        # ... existing code ...

        # ADD: Auth state property
        self._auth_state = "authenticated"  # Default

    @Property(str, notify=authStateChanged)
    def authState(self):
        return self._auth_state

    @authState.setter
    def authState(self, value: str):
        if self._auth_state != value:
            old_state = self._auth_state
            self._auth_state = value
            logger.info(f"Camera {self.id} auth state: {old_state} -> {value}")
            self.authStateChanged.emit()

            # Update camera state based on auth state
            if value == "unauthenticated":
                self.set_state("offline")
            elif value == "authenticated" and self.state == "offline":
                self.set_state("online")  # Or appropriate online state
```

## 7. Usage Examples

### 7.1 How It Works in Practice
```python
# 1. User logs in successfully
api_client = APIClient(server_info)
response = api_client.login("username", "password")
# -> Tokens automatically stored in SimpleTokenManager

# 2. Making API calls
try:
    cameras = api_client.get_cameras()
    # -> If 401, tries refresh once
    # -> If refresh fails, server disconnected, exception raised
except AuthenticationError:
    # Show login dialog
    show_login_dialog(api_client.server_id)

# 3. WebSocket connection
ws_client = WebsocketClient(ws_url, headers, server_ip)
# -> If 401 error, tries refresh once
# -> If refresh fails, stops reconnecting

# 4. Check server status
token_manager = SimpleTokenManager.get_instance()
if not token_manager.is_server_connected(server_id):
    # Show "Please login" message
    show_login_required_message()
```

### 7.2 Benefits of This Lightweight Approach
```python
# ✅ PROS:
# 1. Minimal code changes - just add a few lines to existing files
# 2. Clear disconnect behavior - no ambiguity when refresh fails
# 3. Simple to understand and debug
# 4. Backward compatible - existing code still works
# 5. Easy to test - simple state machine

# ✅ WHAT IT SOLVES:
# 1. ❌ Multiple refresh attempts -> ✅ Try once, fail = disconnect
# 2. ❌ Unclear token state -> ✅ Clear server connected/disconnected state
# 3. ❌ WebSocket keeps reconnecting -> ✅ Stop reconnecting when disconnected
# 4. ❌ No user feedback -> ✅ Clear "login required" notifications
# 5. ❌ Camera state confusion -> ✅ Cameras offline when server disconnected
```

## 8. Testing Strategy - Simple

### 8.1 Test Scenarios
```python
# Test 1: Successful refresh
def test_successful_refresh():
    # Login -> Store tokens -> API call gets 401 -> Refresh succeeds -> Continue

# Test 2: Failed refresh - disconnect
def test_failed_refresh():
    # Login -> Store tokens -> API call gets 401 -> Refresh fails -> Server disconnected

# Test 3: WebSocket behavior
def test_websocket_disconnect():
    # Connected -> 401 error -> Refresh fails -> Stop reconnecting

# Test 4: Multiple servers
def test_multi_server():
    # Server A connected, Server B disconnected -> Independent states

# Test 5: Camera state sync
def test_camera_offline():
    # Server disconnected -> All cameras for that server go offline
```

## 9. Implementation Checklist

### 9.1 Phase 1: Core Implementation (1-2 days)
```python
# ✅ TODO:
# 1. Create src/common/auth/simple_token_manager.py
# 2. Add SimpleTokenManager, ServerInfo, SimpleTokenInfo classes
# 3. Add singleton pattern and basic methods
# 4. Add simple logging

# Files to create:
# - src/common/auth/__init__.py
# - src/common/auth/simple_token_manager.py
```

### 9.2 Phase 2: APIClient Integration (1 day)
```python
# ✅ TODO:
# 1. Add token_manager import to APIClient.__init__
# 2. Add server_id generation
# 3. Modify login() to store tokens
# 4. Modify refresh_access_token() to handle disconnect
# 5. Test with existing login flow

# Files to modify:
# - src/api/api_client.py (minimal changes)
```

### 9.3 Phase 3: WebSocket Integration (1 day)
```python
# ✅ TODO:
# 1. Add token_manager import to WebsocketClient.__init__
# 2. Modify on_error() to use token manager
# 3. Modify on_close() to check server state
# 4. Test WebSocket reconnection behavior

# Files to modify:
# - src/common/websocket/websocket_client.py (minimal changes)
```

### 9.4 Phase 4: UI Integration (1 day)
```python
# ✅ TODO:
# 1. Add notification system for login required
# 2. Add server status checking in UI
# 3. Show clear messages when server disconnected
# 4. Test user experience flow

# Files to modify:
# - UI components that show login dialogs
# - Status indicators
```

## 10. Summary - Lightweight & Effective

### 10.1 What This Achieves
```
✅ BEFORE (Problems):
- Token refresh fails silently
- WebSocket keeps reconnecting forever
- No clear indication when login needed
- Camera states not synced with auth
- Multiple refresh attempts waste resources

✅ AFTER (Solutions):
- Clear disconnect when refresh fails
- WebSocket stops reconnecting when disconnected
- Clear "login required" notifications
- Cameras go offline when server disconnected
- Single refresh attempt - simple and clear
```

### 10.2 Key Benefits
1. **🎯 Simple**: Just 3 classes, minimal code changes
2. **🔒 Reliable**: Clear state machine, no ambiguity
3. **👤 User-Friendly**: Clear feedback when login needed
4. **🔧 Maintainable**: Easy to understand and debug
5. **⚡ Efficient**: No wasted refresh attempts
6. **🔄 Backward Compatible**: Existing code still works

### 10.3 Perfect for Your Use Case
- **Nhẹ nhàng**: Minimal changes to existing code
- **Disconnect khi hết refresh_token**: Exactly what you asked for
- **Per-server token**: Each server managed independently
- **Camera state sync**: Cameras offline when server disconnected
- **Clear user feedback**: No confusion about auth state

**🚀 Ready to implement! Start with Phase 1 - create the SimpleTokenManager.**
