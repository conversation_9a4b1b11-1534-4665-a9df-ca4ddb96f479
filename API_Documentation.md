# API Client và WebSocket Client Documentation

## Tổng quan

Tài liệu này mô tả chức năng của hai file chính trong hệ thống iVMS:
- `src/api/api_client.py` - Client API REST để giao tiếp với server
- `src/common/websocket/websocket_client.py` - Client WebSocket để nhận sự kiện real-time

## 1. API Client (`api_client.py`)

### Mục đích
APIClient là một singleton class chịu trách nhiệm giao tiếp với VMS server thông qua REST API. Nó hỗ trợ cả IP server trực tiếp và gateway/domain server.

### Các thành phần chính

#### 1.1 APIThread
- **Chức năng**: Thread riêng biệt để thực hiện các API request không đồng bộ
- **Kế thừa**: `QtCore.QThread`
- **Signal**: `on_request_done` - ph<PERSON><PERSON> t<PERSON> hiệu khi request hoàn thành

#### 1.2 APIClientSingleton
- **Chức năng**: Metaclass đảm bảo chỉ có một instance của APIClient
- **Pattern**: Singleton pattern

#### 1.3 APIClient Class

##### Khởi tạo và cấu hình
```python
def __init__(self, server: ServerInfoModel = None)
```
- Khởi tạo với thông tin server
- Tự động phát hiện IP address vs domain/gateway
- Tạo các URL endpoint tương ứng

##### Quản lý URL và Headers
- `generate_url()`: Tạo các URL endpoint dựa trên loại server
- `get_url(endpoint)`: Lấy URL đầy đủ cho endpoint
- `get_headers()`: Tạo headers phù hợp (IP server vs gateway)

##### Xác thực (Authentication)
- `login(username, password, captcha, captcha_id)`: Đăng nhập
- `refresh_access_token()`: Làm mới access token
- Hỗ trợ nhiều loại server: GPSTECH, CYGATE, và server tùy chỉnh

### 1.4 Các API chính

#### Camera Management
- `get_cameras(activates=True)`: Lấy danh sách camera
- `get_camera(ids)`: Lấy thông tin camera theo ID
- `create_camera(data)`: Tạo camera mới
- `update_camera_by_put(data)`: Cập nhật camera
- `delete_camera(id, ...)`: Xóa camera
- `get_devices_tree()`: Lấy cây thiết bị

#### Camera Groups
- `get_groups(ids)`: Lấy danh sách nhóm camera
- `create_group(data)`: Tạo nhóm camera
- `update_group_py_put(data)`: Cập nhật nhóm
- `delete_group(id, ...)`: Xóa nhóm
- `add_camera_to_group(data)`: Thêm camera vào nhóm

#### AI Flows
- `get_aiflows(id, cameraIds, ...)`: Lấy danh sách AI flow
- `create_aiflows(data)`: Tạo AI flow
- `update_aiflows(data)`: Cập nhật AI flow
- `delete_aiflows(ids)`: Xóa AI flow
- `apply_ai_flow(data)`: Áp dụng AI flow cho nhóm
- `apply_ai_flow_camera(data)`: Áp dụng AI flow cho camera

#### Streaming
- `get_stream_url(cameraId, format, ...)`: Lấy URL stream
- `get_ai_stream_url(aiFlowId, ...)`: Lấy URL stream AI

#### Recording
- `get_videos(ids, dateFrom, dateTo, ...)`: Lấy danh sách video
- `export_video(data)`: Export video
- `get_recording_schedule(ids, cameraIds)`: Lấy lịch ghi hình
- `create_recording_schedule(data)`: Tạo lịch ghi hình
- `update_recording_schedule_by_patch(data)`: Cập nhật lịch

#### Events
- `get_events(dateFrom, dateTo, ...)`: Lấy danh sách sự kiện
- `get_events_callback(...)`: Lấy sự kiện với callback

#### Zones (Polygons)
- `get_zones(ids, type, aiFlowIds)`: Lấy danh sách vùng
- `create_zones(data)`: Tạo vùng
- `update_zones(data)`: Cập nhật vùng
- `delete_zones(id, cameraIds, type)`: Xóa vùng
- `add_polygons_to_aiflow(data)`: Thêm polygon vào AI flow

#### Camera Discovery
- `search_single(data)`: Tìm kiếm camera đơn lẻ
- `discovered_camera(data)`: Cập nhật camera được phát hiện
- `add_discovered_camera(data)`: Thêm camera được phát hiện

## 2. WebSocket Client (`websocket_client.py`)

### Mục đích
WebSocketClient quản lý kết nối WebSocket real-time với server để nhận các sự kiện như cảnh báo, thông báo, và cập nhật trạng thái.

### Các thành phần chính

#### 2.1 WebsocketClient Class
- **Generic Type**: `Generic[THandler]` - hỗ trợ nhiều loại handler
- **WebSocket Library**: Sử dụng `websocket-client`

#### 2.2 Khởi tạo
```python
def __init__(self, url: str, header: Optional[dict] = None, server_ip: Optional[str] = None)
```
- Tạo WebSocketApp instance
- Khởi tạo MessageProcessor để xử lý message
- Thiết lập các callback handlers

#### 2.3 Quản lý kết nối
- `connect()`: Kết nối đồng bộ
- `connect_background()`: Kết nối trong background thread
- `close()`: Đóng kết nối
- Auto-reconnect khi mất kết nối (trừ lỗi authentication)

#### 2.4 Event Handlers

##### `on_open(ws)`
- Được gọi khi kết nối thành công
- Phát event `connection_established`

##### `on_message(ws, message)`
- Nhận và xử lý message từ server
- Parse JSON và đưa vào message queue
- Sử dụng MessageProcessor để xử lý bất đồng bộ

##### `on_error(ws, error)`
- Xử lý lỗi kết nối
- Đặc biệt xử lý lỗi UNAUTHORIZED (401)
- Phát event `connection_error`

##### `on_close(ws, close_status_code, close_msg)`
- Xử lý khi kết nối bị đóng
- Auto-reconnect nếu không phải lỗi authentication
- Phát event `connection_lost`

#### 2.5 Authentication Handling
- `_auth_error` flag để track lỗi xác thực
- `_handle_unauthorized_error()`: Xử lý khi token hết hạn
- `reset_auth_error()`: Reset flag sau khi login thành công
- Không auto-refresh token, yêu cầu user login lại

#### 2.6 Handler Management
- `subscribe(handler)`: Đăng ký handler để nhận events
- `send(data)`: Gửi data qua WebSocket
- Hỗ trợ multiple handlers với event filtering

### 2.7 Message Processing
- Sử dụng `MessageProcessor` để xử lý message bất đồng bộ
- Message queue để tránh block UI thread
- Event-driven architecture với các EventType

## 3. Tích hợp và Sử dụng

### Workflow điển hình:
1. **Khởi tạo APIClient** với server info
2. **Login** qua APIClient để lấy access token
3. **Khởi tạo WebSocketClient** với token trong header
4. **Subscribe handlers** để nhận events
5. **Sử dụng API methods** để thao tác với hệ thống
6. **Xử lý real-time events** qua WebSocket

### Error Handling:
- API errors được handle trong từng method
- WebSocket auto-reconnect trừ authentication errors
- Logging comprehensive cho debugging
- Thread-safe operations

## 4. Cấu hình Server

### IP Server vs Gateway:
- **IP Server**: Kết nối trực tiếp, không cần authentication phức tạp
- **Gateway/Domain**: Qua HTTPS, cần full authentication flow
- Tự động detect và config phù hợp

### Supported Servers:
- GPSTECH server với authorization riêng
- CYGATE server với captcha support
- Custom servers với flexible config
