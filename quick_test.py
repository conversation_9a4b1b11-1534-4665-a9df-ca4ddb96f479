"""
Quick test to verify the fixes
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_enhanced_api_client():
    """Test Enhanced API Client creation"""
    print("🧪 Testing Enhanced API Client...")
    
    try:
        from src.api.enhanced_api_client import EnhancedAPIClient
        from src.common.server.server_info import ServerInfoModel, ServerInfo

        # Create server info
        server_data = ServerInfo()
        server_data.server_ip = "*************"
        server_data.server_port = 8080
        server_info = ServerInfoModel(server=server_data)
        
        # Create enhanced API client
        api_client = EnhancedAPIClient(server=server_info)
        
        print(f"✅ EnhancedAPIClient created")
        print(f"✅ Server ID: {api_client.server_id}")
        print(f"✅ Token Manager: {type(api_client.token_manager).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_controller_integration():
    """Test Controller integration"""
    print("\n🧪 Testing Controller Integration...")
    
    try:
        from src.common.controller.controller_manager import Controller
        from src.common.server.server_info import ServerInfoModel, ServerInfo
        from src.api.enhanced_api_client import EnhancedAPIClient

        # Create server info
        server_data = ServerInfo()
        server_data.server_ip = "*************"
        server_data.server_port = 8080
        server_data.username = "admin"
        server_data.password = "password"
        server_info = ServerInfoModel(server=server_data)
        
        # Create controller
        controller = Controller(server=server_info)
        
        print(f"✅ Controller created")
        print(f"✅ API Client type: {type(controller.api_client).__name__}")
        print(f"✅ Is EnhancedAPIClient: {isinstance(controller.api_client, EnhancedAPIClient)}")
        
        if hasattr(controller.api_client, 'server_id'):
            print(f"✅ Server ID: {controller.api_client.server_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_token_coordination():
    """Test token manager coordination"""
    print("\n🧪 Testing Token Manager Coordination...")
    
    try:
        from src.common.auth.simple_token_manager import SimpleTokenManager
        from src.api.enhanced_api_client import EnhancedAPIClient
        from src.common.websocket.enhanced_websocket_client import EnhancedWebsocketClient
        from src.common.server.server_info import ServerInfoModel, ServerInfo

        # Clear token manager
        tm = SimpleTokenManager.get_instance()
        tm.clear_all_servers()

        # Create server info
        server_data = ServerInfo()
        server_data.server_ip = "192.168.1.test"
        server_data.server_port = 8080
        server_info = ServerInfoModel(server=server_data)
        
        # Create components
        api_client = EnhancedAPIClient(server=server_info)
        ws_client = EnhancedWebsocketClient(
            url="wss://192.168.1.test:8081/socket",
            server_ip="192.168.1.test"
        )
        
        print(f"✅ Components created")
        print(f"✅ API Server ID: {api_client.server_id}")
        print(f"✅ WS Server ID: {ws_client.server_id}")
        
        # Test token storage
        server_id = "192.168.1.test:8080"
        tm.store_tokens(
            server_id=server_id,
            access_token="test_token",
            refresh_token="test_refresh",
            expires_in=3600
        )
        
        # Test token retrieval
        api_token = api_client.token_manager.get_access_token(server_id)
        ws_token = ws_client.token_manager.get_access_token(server_id)
        
        print(f"✅ API Token: {api_token}")
        print(f"✅ WS Token: {ws_token}")
        print(f"✅ Tokens match: {api_token == ws_token}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run quick tests"""
    print("🚀 Quick Integration Test")
    
    tests = [
        ("Enhanced API Client", test_enhanced_api_client),
        ("Controller Integration", test_controller_integration),
        ("Token Coordination", test_token_coordination)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        result = test_func()
        results.append((test_name, result))
    
    print("\n📊 Results:")
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nSuccess Rate: {passed}/{len(tests)} ({passed/len(tests)*100:.1f}%)")
    
    if passed == len(tests):
        print("\n🎉 ALL TESTS PASSED! Integration is working!")
    else:
        print(f"\n⚠️ {len(tests) - passed} tests failed. Check errors above.")


if __name__ == "__main__":
    main()
