"""
Authentication UI Manager - Phase 4 Integration
Provides UI notifications and status updates for authentication events
"""

import logging
from typing import Op<PERSON>, Dict, Any
from PySide6.QtCore import QObject, Signal, QTimer
from PySide6.QtWidgets import QWidget

from .simple_token_manager import SimpleTokenManager, ServerAuthState
from src.common.widget.notifications.notify import Notifications
from src.common.controller.main_controller import main_controller
from src.styles.style import Style

logger = logging.getLogger(__name__)


class AuthUIManager(QObject):
    """
    Manages UI notifications and updates for authentication events
    
    Key Features:
    1. ✅ Login required notifications
    2. ✅ Server status updates in UI
    3. ✅ Camera state synchronization
    4. ✅ User-friendly error messages
    """
    
    # Signals for UI updates
    login_required = Signal(str, str)  # server_id, reason
    server_status_changed = Signal(str, str)  # server_id, status
    token_refreshed = Signal(str)  # server_id
    authentication_error = Signal(str, str)  # server_id, error_message
    
    _instance = None
    
    @classmethod
    def get_instance(cls):
        """Singleton pattern"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def __init__(self):
        super().__init__()
        if AuthUIManager._instance is not None:
            raise Exception("Use get_instance() instead")
        
        self.token_manager = SimpleTokenManager.get_instance()
        self._notification_parent = None
        self._server_status_widgets: Dict[str, QWidget] = {}
        self._status_check_timer = QTimer()
        self._status_check_timer.timeout.connect(self._check_server_statuses)
        self._status_check_timer.start(30000)  # Check every 30 seconds
        
        # Connect to token manager events (we'll add these to token manager)
        self._setup_token_manager_connections()
        
        logger.info("🎨 AuthUIManager initialized")
    
    def set_notification_parent(self, parent: QWidget):
        """Set parent widget for notifications"""
        self._notification_parent = parent
        logger.info(f"🎨 Notification parent set: {type(parent).__name__}")
    
    def register_server_status_widget(self, server_id: str, widget: QWidget):
        """Register widget to update when server status changes"""
        self._server_status_widgets[server_id] = widget
        logger.debug(f"📊 Registered status widget for server {server_id}")
    
    def show_login_required_notification(self, server_id: str, reason: str):
        """Show user-friendly login required notification"""
        try:
            if not self._notification_parent:
                logger.warning("⚠️ No notification parent set")
                return
            
            # Create user-friendly message
            server_name = self._get_friendly_server_name(server_id)
            title = f"Login Required - {server_name}"
            
            # Show notification
            Notifications(
                parent=self._notification_parent,
                title=title,
                icon=Style.PrimaryImage.fail_result,
                time=5000  # Show for 5 seconds
            )
            
            # Emit signal for other UI components
            self.login_required.emit(server_id, reason)
            
            logger.info(f"📢 Login required notification shown for {server_id}: {reason}")
            
        except Exception as e:
            logger.error(f"❌ Failed to show login notification: {e}")
    
    def show_token_refresh_notification(self, server_id: str, success: bool):
        """Show token refresh status notification"""
        try:
            if not self._notification_parent:
                return
            
            server_name = self._get_friendly_server_name(server_id)
            
            if success:
                title = f"Session Renewed - {server_name}"
                icon = Style.PrimaryImage.sucess_result
                self.token_refreshed.emit(server_id)
            else:
                title = f"Session Expired - {server_name}"
                icon = Style.PrimaryImage.fail_result
                self.authentication_error.emit(server_id, "Token refresh failed")
            
            Notifications(
                parent=self._notification_parent,
                title=title,
                icon=icon,
                time=3000
            )
            
            logger.info(f"🔄 Token refresh notification shown for {server_id}: {success}")
            
        except Exception as e:
            logger.error(f"❌ Failed to show refresh notification: {e}")
    
    def show_server_connection_notification(self, server_id: str, connected: bool, reason: str = None):
        """Show server connection status notification"""
        try:
            if not self._notification_parent:
                return
            
            server_name = self._get_friendly_server_name(server_id)
            
            if connected:
                title = f"Connected - {server_name}"
                icon = Style.PrimaryImage.sucess_result
            else:
                title = f"Disconnected - {server_name}"
                if reason:
                    title += f" ({reason})"
                icon = Style.PrimaryImage.fail_result
            
            Notifications(
                parent=self._notification_parent,
                title=title,
                icon=icon,
                time=3000
            )
            
            # Emit signal for UI updates
            status = "connected" if connected else "disconnected"
            self.server_status_changed.emit(server_id, status)
            
            logger.info(f"🔗 Connection notification shown for {server_id}: {connected}")
            
        except Exception as e:
            logger.error(f"❌ Failed to show connection notification: {e}")
    
    def update_server_status_widgets(self):
        """Update all registered server status widgets"""
        try:
            for server_id, widget in self._server_status_widgets.items():
                if widget and hasattr(widget, 'update_auth_status'):
                    server_state = self.token_manager.get_server_state(server_id)
                    connected = server_state == ServerAuthState.CONNECTED
                    widget.update_auth_status(connected, server_state.value)
            
        except Exception as e:
            logger.error(f"❌ Failed to update status widgets: {e}")
    
    def get_server_status_summary(self) -> Dict[str, Any]:
        """Get summary of all server statuses for UI display"""
        try:
            all_servers = self.token_manager.get_all_servers()
            connected_count = len(self.token_manager.get_connected_servers())
            total_count = len(all_servers)
            disconnected_count = total_count - connected_count
            
            return {
                "total_servers": total_count,
                "connected_count": connected_count,
                "disconnected_count": disconnected_count,
                "servers": {
                    server_id: {
                        "state": server_info.auth_state.value,
                        "connected": server_info.auth_state == ServerAuthState.CONNECTED,
                        "error": server_info.last_error,
                        "friendly_name": self._get_friendly_server_name(server_id)
                    }
                    for server_id, server_info in all_servers.items()
                }
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get server status summary: {e}")
            return {"total_servers": 0, "connected_count": 0, "disconnected_count": 0, "servers": {}}
    
    def show_authentication_error(self, server_id: str, error_message: str):
        """Show authentication error notification"""
        try:
            if not self._notification_parent:
                return
            
            server_name = self._get_friendly_server_name(server_id)
            title = f"Authentication Error - {server_name}"
            
            Notifications(
                parent=self._notification_parent,
                title=title,
                icon=Style.PrimaryImage.fail_result,
                time=4000
            )
            
            self.authentication_error.emit(server_id, error_message)
            
            logger.warning(f"🚨 Authentication error notification for {server_id}: {error_message}")
            
        except Exception as e:
            logger.error(f"❌ Failed to show auth error notification: {e}")
    
    def _get_friendly_server_name(self, server_id: str) -> str:
        """Get user-friendly server name"""
        try:
            # Try to get server info from server manager
            from src.common.server.server_info import server_info_model_manager
            
            for server_info in server_info_model_manager.server_list.values():
                if f"{server_info.server_ip}:{server_info.server_port}" == server_id:
                    return server_info.server_name or server_info.server_ip
            
            # Fallback to server_id
            return server_id.split(':')[0]  # Just IP part
            
        except Exception:
            return server_id.split(':')[0]
    
    def _setup_token_manager_connections(self):
        """Setup connections to token manager events"""
        # Note: We'll need to add these signals to SimpleTokenManager
        # For now, we'll use a polling approach with the timer
        pass
    
    def _check_server_statuses(self):
        """Periodically check server statuses and update UI"""
        try:
            # Update status widgets
            self.update_server_status_widgets()
            
            # Check for any servers that need attention
            all_servers = self.token_manager.get_all_servers()
            for server_id, server_info in all_servers.items():
                if server_info.auth_state == ServerAuthState.DISCONNECTED and server_info.last_error:
                    # Server is disconnected with an error - might need user attention
                    # We could show a notification here, but let's avoid spam
                    pass
            
        except Exception as e:
            logger.error(f"❌ Error checking server statuses: {e}")
    
    def force_refresh_ui(self):
        """Force refresh of all UI components"""
        try:
            self.update_server_status_widgets()
            
            # Emit status changed signals for all servers
            all_servers = self.token_manager.get_all_servers()
            for server_id, server_info in all_servers.items():
                status = "connected" if server_info.auth_state == ServerAuthState.CONNECTED else "disconnected"
                self.server_status_changed.emit(server_id, status)
            
            logger.info("🔄 UI refresh forced")
            
        except Exception as e:
            logger.error(f"❌ Failed to force UI refresh: {e}")
    
    def cleanup(self):
        """Cleanup resources"""
        try:
            self._status_check_timer.stop()
            self._server_status_widgets.clear()
            logger.info("🧹 AuthUIManager cleaned up")
            
        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")
    
    def __str__(self):
        """String representation for debugging"""
        summary = self.get_server_status_summary()
        return f"AuthUIManager(servers={summary['total_servers']}, connected={summary['connected_count']})"
