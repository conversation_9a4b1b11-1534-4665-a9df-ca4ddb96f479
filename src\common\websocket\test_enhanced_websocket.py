"""
Test Enhanced WebSocket Client - Phase 3 Integration
Verify WebSocket coordination with SimpleTokenManager
"""

import sys
import os
import logging
import time
from unittest.mock import Mock, patch, MagicMock

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from src.common.websocket.enhanced_websocket_client import EnhancedWebsocketClient
from src.common.auth.simple_token_manager import SimpleTokenManager, ServerAuthState

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class MockController:
    """Mock Controller for testing"""
    def __init__(self, should_refresh_succeed=True):
        self.should_refresh_succeed = should_refresh_succeed
        self.access_token = "new_websocket_token"
    
    def refresh_access_token(self):
        if self.should_refresh_succeed:
            return self.access_token
        return None


def test_websocket_server_state_coordination():
    """Test WebSocket coordination with server authentication state"""
    print("\n" + "="*70)
    print("🧪 Testing WebSocket Server State Coordination")
    print("="*70)
    
    # Clear token manager
    tm = SimpleTokenManager.get_instance()
    tm.clear_all_servers()
    
    server_id = "*************:8080"
    server_ip = "*************"
    
    # Store valid tokens
    tm.store_tokens(
        server_id=server_id,
        access_token="websocket_access_token",
        refresh_token="websocket_refresh_token",
        expires_in=3600
    )
    
    # Mock WebSocket dependencies
    with patch('websocket.WebSocketApp'), \
         patch('src.common.websocket.enhanced_websocket_client.controller_manager') as mock_controller_manager:
        
        # Create enhanced WebSocket client
        ws_client = EnhancedWebsocketClient(
            url=f"wss://{server_ip}:8081/socket",
            header={"Authorization": "Bearer websocket_access_token"},
            server_ip=server_ip
        )
        
        # Test server state checking
        assert ws_client.token_manager.is_server_connected(server_id), "Server should be connected"
        assert ws_client._should_reconnect(), "Should allow reconnection when server connected"
        
        # Disconnect server
        tm._disconnect_server(server_id, "Test disconnect")
        
        # Test state coordination
        assert not ws_client.token_manager.is_server_connected(server_id), "Server should be disconnected"
        assert not ws_client._should_reconnect(), "Should not reconnect when server disconnected"
        
        print("✅ WebSocket server state coordination works")


def test_websocket_token_refresh_coordination():
    """Test WebSocket token refresh coordination with token manager"""
    print("\n" + "="*70)
    print("🧪 Testing WebSocket Token Refresh Coordination")
    print("="*70)
    
    tm = SimpleTokenManager.get_instance()
    server_id = "*************:8080"
    server_ip = "*************"
    
    # Store tokens
    tm.store_tokens(
        server_id=server_id,
        access_token="old_websocket_token",
        refresh_token="websocket_refresh_token",
        expires_in=3600
    )
    
    # Mock successful controller
    mock_controller = MockController(should_refresh_succeed=True)
    
    with patch('websocket.WebSocketApp'), \
         patch('src.common.websocket.enhanced_websocket_client.controller_manager') as mock_controller_manager:
        
        mock_controller_manager.get_controller.return_value = mock_controller
        
        # Create WebSocket client
        ws_client = EnhancedWebsocketClient(
            url=f"wss://{server_ip}:8081/socket",
            server_ip=server_ip
        )
        
        # Test token refresh
        result = ws_client.refresh_token()
        
        assert result == True, "Token refresh should succeed"
        assert "Authorization" in ws_client.header, "Should have Authorization header"
        assert "new_websocket_token" in ws_client.header["Authorization"], "Should have new token"
        
        print("✅ WebSocket token refresh coordination works")


def test_websocket_stop_reconnect_when_disconnected():
    """Test WebSocket stops reconnecting when server is disconnected"""
    print("\n" + "="*70)
    print("🧪 Testing WebSocket Stop Reconnect When Server Disconnected")
    print("="*70)
    
    tm = SimpleTokenManager.get_instance()
    server_id = "*************:8080"
    server_ip = "*************"
    
    # Store tokens initially
    tm.store_tokens(
        server_id=server_id,
        access_token="websocket_token",
        refresh_token="websocket_refresh",
        expires_in=3600
    )
    
    with patch('websocket.WebSocketApp') as mock_ws_app, \
         patch('src.common.websocket.enhanced_websocket_client.controller_manager'):
        
        # Create WebSocket client
        ws_client = EnhancedWebsocketClient(
            url=f"wss://{server_ip}:8081/socket",
            server_ip=server_ip
        )
        
        # Initially should allow reconnection
        assert ws_client._should_reconnect(), "Should initially allow reconnection"
        
        # Disconnect server (simulate refresh failure)
        tm._disconnect_server(server_id, "Refresh token expired")
        
        # Now should not reconnect
        assert not ws_client._should_reconnect(), "Should not reconnect when server disconnected"
        
        # Test on_close behavior
        with patch.object(ws_client, 'connect_background') as mock_connect:
            ws_client.on_close(None, 1000, "Normal closure")
            
            # Should not attempt to reconnect
            mock_connect.assert_not_called()
        
        print("✅ WebSocket stops reconnecting when server disconnected")


def test_websocket_401_error_handling():
    """Test WebSocket 401 error handling with token manager"""
    print("\n" + "="*70)
    print("🧪 Testing WebSocket 401 Error Handling")
    print("="*70)
    
    tm = SimpleTokenManager.get_instance()
    server_id = "*************:8080"
    server_ip = "*************"
    
    # Store tokens
    tm.store_tokens(
        server_id=server_id,
        access_token="websocket_token",
        refresh_token="websocket_refresh",
        expires_in=3600
    )
    
    # Mock successful controller
    mock_controller = MockController(should_refresh_succeed=True)
    
    with patch('websocket.WebSocketApp'), \
         patch('src.common.websocket.enhanced_websocket_client.controller_manager') as mock_controller_manager:
        
        mock_controller_manager.get_controller.return_value = mock_controller
        
        # Create WebSocket client
        ws_client = EnhancedWebsocketClient(
            url=f"wss://{server_ip}:8081/socket",
            server_ip=server_ip
        )
        
        # Simulate 401 error
        from websocket._exceptions import WebSocketBadStatusException
        from http import HTTPStatus
        
        error_401 = WebSocketBadStatusException("Unauthorized", HTTPStatus.UNAUTHORIZED, None, None)
        
        # Test error handling
        ws_client.on_error(None, error_401)
        
        # Should have updated headers with new token
        assert "Authorization" in ws_client.header, "Should have Authorization header"
        assert "new_websocket_token" in ws_client.header["Authorization"], "Should have refreshed token"
        
        print("✅ WebSocket 401 error handling works")


def test_websocket_failed_refresh_disconnect():
    """Test WebSocket behavior when token refresh fails"""
    print("\n" + "="*70)
    print("🧪 Testing WebSocket Failed Refresh → Disconnect")
    print("="*70)
    
    tm = SimpleTokenManager.get_instance()
    server_id = "*************:8080"
    server_ip = "*************"
    
    # Store tokens
    tm.store_tokens(
        server_id=server_id,
        access_token="websocket_token",
        refresh_token="websocket_refresh",
        expires_in=3600
    )
    
    # Mock failed controller
    mock_controller = MockController(should_refresh_succeed=False)
    
    with patch('websocket.WebSocketApp'), \
         patch('src.common.websocket.enhanced_websocket_client.controller_manager') as mock_controller_manager:
        
        mock_controller_manager.get_controller.return_value = mock_controller
        
        # Create WebSocket client
        ws_client = EnhancedWebsocketClient(
            url=f"wss://{server_ip}:8081/socket",
            server_ip=server_ip
        )
        
        # Initially connected
        assert tm.is_server_connected(server_id), "Server should be initially connected"
        
        # Simulate 401 error with failed refresh
        from websocket._exceptions import WebSocketBadStatusException
        from http import HTTPStatus
        
        error_401 = WebSocketBadStatusException("Unauthorized", HTTPStatus.UNAUTHORIZED, None, None)
        ws_client.on_error(None, error_401)
        
        # Server should be disconnected after failed refresh
        assert not tm.is_server_connected(server_id), "Server should be disconnected after failed refresh"
        assert not ws_client._should_reconnect(), "Should not reconnect after failed refresh"
        
        print("✅ WebSocket failed refresh disconnect works")


def test_websocket_connection_status_monitoring():
    """Test WebSocket connection status monitoring"""
    print("\n" + "="*70)
    print("🧪 Testing WebSocket Connection Status Monitoring")
    print("="*70)
    
    tm = SimpleTokenManager.get_instance()
    server_id = "*************:8080"
    server_ip = "*************"
    
    # Store tokens
    tm.store_tokens(
        server_id=server_id,
        access_token="monitor_token",
        refresh_token="monitor_refresh",
        expires_in=3600
    )
    
    with patch('websocket.WebSocketApp'):
        # Create WebSocket client
        ws_client = EnhancedWebsocketClient(
            url=f"wss://{server_ip}:8081/socket",
            header={"Authorization": "Bearer monitor_token"},
            server_ip=server_ip
        )
        
        # Get connection status
        status = ws_client.get_connection_status()
        
        assert status["server_id"] == server_id, "Should have correct server ID"
        assert status["server_connected"] == True, "Should show server connected"
        assert status["server_state"] == "connected", "Should show connected state"
        assert status["reconnect_enabled"] == True, "Should have reconnect enabled"
        assert status["has_auth_header"] == True, "Should have auth header"
        
        print("✅ WebSocket connection status monitoring works")
        print(f"📊 Status: {status}")


def test_websocket_force_reconnect():
    """Test WebSocket force reconnect functionality"""
    print("\n" + "="*70)
    print("🧪 Testing WebSocket Force Reconnect")
    print("="*70)
    
    tm = SimpleTokenManager.get_instance()
    server_id = "*************:8080"
    server_ip = "*************"
    
    # Store tokens
    tm.store_tokens(
        server_id=server_id,
        access_token="reconnect_token",
        refresh_token="reconnect_refresh",
        expires_in=3600
    )
    
    with patch('websocket.WebSocketApp') as mock_ws_app:
        # Create WebSocket client
        ws_client = EnhancedWebsocketClient(
            url=f"wss://{server_ip}:8081/socket",
            server_ip=server_ip
        )
        
        # Disable reconnect
        ws_client._disable_reconnect("Test disable")
        assert not ws_client._reconnect_enabled, "Reconnect should be disabled"
        
        # Force reconnect
        with patch.object(ws_client, 'close'), \
             patch.object(ws_client, 'connect_background') as mock_connect:
            
            ws_client.force_reconnect()
            
            # Should re-enable reconnect and attempt connection
            assert ws_client._reconnect_enabled, "Reconnect should be re-enabled"
            mock_connect.assert_called_once()
        
        print("✅ WebSocket force reconnect works")


def main():
    """Run all Phase 3 tests"""
    print("🚀 Starting Enhanced WebSocket Client Tests - Phase 3")
    
    try:
        test_websocket_server_state_coordination()
        test_websocket_token_refresh_coordination()
        test_websocket_stop_reconnect_when_disconnected()
        test_websocket_401_error_handling()
        test_websocket_failed_refresh_disconnect()
        test_websocket_connection_status_monitoring()
        test_websocket_force_reconnect()
        
        print("\n" + "="*70)
        print("🎉 ALL PHASE 3 TESTS PASSED!")
        print("="*70)
        
        print("\n✅ Key Features Verified:")
        print("   🔗 WebSocket server state coordination")
        print("   🔄 Token refresh coordination with token manager")
        print("   🚫 Stop reconnecting when server disconnected")
        print("   🔐 Enhanced 401 error handling")
        print("   📊 Connection status monitoring")
        print("   🔄 Force reconnect functionality")
        
        print("\n🎯 Phase 3 Benefits:")
        print("   • WebSocket stops wasting resources on disconnected servers")
        print("   • Coordinated token management between API and WebSocket")
        print("   • Better error handling and user feedback")
        print("   • Graceful connection management")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
