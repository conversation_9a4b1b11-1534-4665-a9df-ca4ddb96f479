"""
Enhanced API Client with SimpleTokenManager Integration
Phase 2: Solve token expiration check and proactive refresh
"""

import logging
from typing import Optional
from src.api.api_client import APIClient
from src.common.auth.simple_token_manager import SimpleTokenManager
from src.common.server.server_info import ServerInfoModel

logger = logging.getLogger(__name__)


class AuthenticationError(Exception):
    """Authentication failed - need to login again"""
    pass


class EnhancedAPIClient(APIClient):
    """
    Enhanced APIClient with proactive token management
    
    Key Features:
    1. ✅ Token expiration check BEFORE API calls
    2. ✅ Proactive refresh when token expires soon
    3. ✅ Graceful disconnect when refresh fails
    4. ✅ Backward compatible with existing APIClient
    """
    
    def __init__(self, server: ServerInfoModel = None):
        # Initialize parent APIClient
        super().__init__(server)

        # ADD: Token manager integration
        self.token_manager = SimpleTokenManager.get_instance()
        self._server_id = None  # Lazy initialization

        logger.info(f"🔧 EnhancedAPIClient created for server {self.server_id}")

    @property
    def server_id(self) -> str:
        """Lazy-loaded server ID property"""
        if self._server_id is None:
            self._server_id = self._generate_server_id()
        return self._server_id

    def _generate_server_id(self) -> str:
        """Generate unique server ID"""
        try:
            # Ensure server_ip and server_port are available
            if hasattr(self, 'server_ip') and hasattr(self, 'server_port'):
                return f"{self.server_ip}:{self.server_port}"
            elif self.server and hasattr(self.server, 'data'):
                return f"{self.server.data.server_ip}:{self.server.data.server_port}"
            else:
                return "unknown_server:8080"
        except Exception as e:
            logger.warning(f"⚠️ Failed to generate server ID: {e}")
            return "unknown_server:8080"
    
    def login(self, username, password, captcha=None, captcha_id=None):
        """Enhanced login with token manager integration"""
        logger.info(f"🔐 Enhanced login for server {self.server_id}")
        
        # Call parent login method
        response = super().login(username, password, captcha, captcha_id)
        
        # Store tokens in token manager after successful login
        if response and response.status_code == 200 and not self.is_ip_address:
            try:
                # Extract expires_in from response if available
                expires_in = None
                if hasattr(response, 'json'):
                    json_data = response.json()
                    data = json_data.get("data", {})
                    expires_in = data.get("expires_in") or data.get("expiresIn")
                
                # Store tokens with expiration info
                self.token_manager.store_tokens(
                    server_id=self.server_id,
                    access_token=self.access_token,
                    refresh_token=self.refresh_token,
                    server_url=self.server_url,
                    expires_in=expires_in
                )
                
                logger.info(f"✅ Tokens stored in token manager for {self.server_id}")
                
            except Exception as e:
                logger.error(f"❌ Failed to store tokens in token manager: {e}")
        
        return response
    
    def refresh_access_token(self):
        """Enhanced refresh with disconnect on failure"""
        logger.info(f"🔄 Enhanced token refresh for server {self.server_id}")
        
        # Call parent refresh method
        result = super().refresh_access_token()
        
        if result is None:
            # Refresh failed - notify token manager to disconnect
            logger.warning(f"❌ Token refresh failed for {self.server_id}")
            self.token_manager._disconnect_server(
                self.server_id, 
                "Refresh token expired or invalid"
            )
        else:
            # Refresh successful - update token manager
            logger.info(f"✅ Token refresh successful for {self.server_id}")
            self.token_manager.update_access_token(self.server_id, result)
        
        return result
    
    def _ensure_authenticated(self) -> bool:
        """
        KEY METHOD: Ensure valid authentication before API calls
        This solves the proactive refresh problem
        """
        if self.is_ip_address:
            # IP servers don't need authentication
            return True
        
        # Check if server is connected
        if not self.token_manager.is_server_connected(self.server_id):
            logger.warning(f"⚠️ Server {self.server_id} is disconnected")
            return False
        
        # Ensure token is valid (proactive refresh if needed)
        if not self.token_manager.ensure_valid_token(self.server_id, self):
            logger.warning(f"❌ Failed to ensure valid token for {self.server_id}")
            return False
        
        return True
    
    def _make_authenticated_request(self, method: str, url: str, **kwargs):
        """
        Enhanced request method with proactive token validation
        This is the core enhancement that solves both problems
        """
        # ✅ SOLUTION 1: Check token expiration BEFORE API call
        if not self._ensure_authenticated():
            raise AuthenticationError(f"Server {self.server_id} disconnected. Please login again.")
        
        # Make the actual request
        try:
            if method.upper() == "GET":
                response = self._make_get_request(url, **kwargs)
            elif method.upper() == "POST":
                response = self._make_post_request(url, **kwargs)
            elif method.upper() == "PUT":
                response = self._make_put_request(url, **kwargs)
            elif method.upper() == "DELETE":
                response = self._make_delete_request(url, **kwargs)
            elif method.upper() == "PATCH":
                response = self._make_patch_request(url, **kwargs)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            # ✅ Handle 401 as backup (shouldn't happen with proactive refresh)
            if response.status_code == 401:
                logger.warning(f"🚫 Got 401 despite proactive refresh for {self.server_id}")
                
                # Try refresh once more as last resort
                if self.token_manager.try_refresh_token(self.server_id, self):
                    logger.info(f"🔄 Last resort refresh successful, retrying request")
                    # Retry the request once
                    if method.upper() == "GET":
                        response = self._make_get_request(url, **kwargs)
                    elif method.upper() == "POST":
                        response = self._make_post_request(url, **kwargs)
                    # ... other methods
                else:
                    logger.error(f"❌ Last resort refresh failed for {self.server_id}")
                    raise AuthenticationError("Authentication failed. Please login again.")
            
            return response
            
        except Exception as e:
            if "authentication" in str(e).lower() or "401" in str(e):
                raise AuthenticationError(f"Authentication error: {e}")
            else:
                raise e
    
    def _make_get_request(self, url: str, **kwargs):
        """Make GET request with current headers"""
        headers = kwargs.get('headers', {})
        headers.update(self.get_headers())
        kwargs['headers'] = headers
        
        import requests
        return requests.get(url, **kwargs)
    
    def _make_post_request(self, url: str, **kwargs):
        """Make POST request with current headers"""
        headers = kwargs.get('headers', {})
        headers.update(self.get_headers())
        kwargs['headers'] = headers
        
        import requests
        return requests.post(url, **kwargs)
    
    def _make_put_request(self, url: str, **kwargs):
        """Make PUT request with current headers"""
        headers = kwargs.get('headers', {})
        headers.update(self.get_headers())
        kwargs['headers'] = headers
        
        import requests
        return requests.put(url, **kwargs)
    
    def _make_delete_request(self, url: str, **kwargs):
        """Make DELETE request with current headers"""
        headers = kwargs.get('headers', {})
        headers.update(self.get_headers())
        kwargs['headers'] = headers
        
        import requests
        return requests.delete(url, **kwargs)
    
    def _make_patch_request(self, url: str, **kwargs):
        """Make PATCH request with current headers"""
        headers = kwargs.get('headers', {})
        headers.update(self.get_headers())
        kwargs['headers'] = headers
        
        import requests
        return requests.patch(url, **kwargs)
    
    # ✅ Enhanced versions of key API methods
    def get_cameras(self, activates=True):
        """Enhanced get_cameras with proactive token management"""
        try:
            url = self.get_url("/api/cameras")
            params = {"activates": activates} if activates is not None else {}
            
            # Use enhanced request method
            response = self._make_authenticated_request("GET", url, params=params)
            return response
            
        except AuthenticationError:
            logger.error(f"❌ Authentication failed for get_cameras on {self.server_id}")
            raise
        except Exception as e:
            logger.error(f"❌ get_cameras error for {self.server_id}: {e}")
            return None
    
    def get_stream_url(self, cameraId=None, format="FLV", isPublic=True, streamIndex=0):
        """Enhanced get_stream_url with proactive token management"""
        try:
            url = self.get_url("/api/stream")
            params = {}
            if cameraId is not None:
                params["cameraId"] = cameraId
            if format is not None:
                params["format"] = format
            if isPublic is not None:
                params["isPublic"] = isPublic
            
            # Use enhanced request method
            response = self._make_authenticated_request("GET", url, params=params)
            return (response, streamIndex)
            
        except AuthenticationError:
            logger.error(f"❌ Authentication failed for get_stream_url on {self.server_id}")
            raise
        except Exception as e:
            logger.error(f"❌ get_stream_url error for {self.server_id}: {e}")
            return (None, streamIndex)
    
    def get_server_status(self) -> dict:
        """Get server authentication status for monitoring"""
        return {
            "server_id": self.server_id,
            "connected": self.token_manager.is_server_connected(self.server_id),
            "state": self.token_manager.get_server_state(self.server_id).value,
            "error": self.token_manager.get_server_error(self.server_id),
            "token_info": self._get_token_debug_info()
        }
    
    def _get_token_debug_info(self) -> Optional[dict]:
        """Get token information for debugging"""
        token = self.token_manager.get_token_info(self.server_id)
        if token:
            return {
                "expires_at": token.expires_at.isoformat() if token.expires_at else None,
                "expires_soon": token.expires_soon(),
                "is_expired": token.is_expired(),
                "time_until_expiry": str(token.time_until_expiry()) if token.time_until_expiry() else None
            }
        return None
