"""
Authentication Manager - Centralized token and server authentication state management

This module provides centralized management for:
1. Server authentication states
2. Token expiration notifications
3. Camera state updates when servers disconnect
4. UI notifications for login requirements
"""

import logging
from typing import Dict, List, Optional, Callable
from enum import Enum
from PySide6.QtCore import QObject, Signal
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)


class ServerAuthState(Enum):
    """Server authentication states"""
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    TOKEN_EXPIRED = "token_expired"
    REFRESH_FAILED = "refresh_failed"


@dataclass
class ServerInfo:
    """Information about a server's authentication state"""
    server_ip: str
    server_url: str
    auth_state: ServerAuthState
    last_token_refresh: Optional[datetime] = None
    last_error: Optional[str] = None
    camera_ids: List[str] = None
    
    def __post_init__(self):
        if self.camera_ids is None:
            self.camera_ids = []


class AuthManager(QObject):
    """
    Centralized Authentication Manager
    
    Manages authentication state for all servers and coordinates
    token refresh, disconnection handling, and UI notifications.
    """
    
    # Signals for UI components
    server_disconnected = Signal(str, str)  # server_ip, reason
    login_required = Signal(str, str)       # server_ip, reason
    camera_state_changed = Signal(str, str, str)  # server_ip, camera_id, new_state
    
    _instance = None
    
    def __init__(self):
        super().__init__()
        self._servers: Dict[str, ServerInfo] = {}
        self._notification_callbacks: List[Callable] = []
        
    @classmethod
    def get_instance(cls):
        """Get singleton instance"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def register_server(self, server_ip: str, server_url: str, camera_ids: List[str] = None):
        """Register a server for authentication management"""
        if camera_ids is None:
            camera_ids = []
            
        self._servers[server_ip] = ServerInfo(
            server_ip=server_ip,
            server_url=server_url,
            auth_state=ServerAuthState.CONNECTED,
            camera_ids=camera_ids.copy()
        )
        
        print(f"📝 [AUTH_MANAGER] Registered server: {server_ip} with {len(camera_ids)} cameras")
        logger.info(f"📝 [AUTH_MANAGER] Registered server: {server_ip} with {len(camera_ids)} cameras")
    
    def update_server_cameras(self, server_ip: str, camera_ids: List[str]):
        """Update camera list for a server"""
        if server_ip in self._servers:
            self._servers[server_ip].camera_ids = camera_ids.copy()
            print(f"📝 [AUTH_MANAGER] Updated cameras for server {server_ip}: {len(camera_ids)} cameras")
            logger.info(f"📝 [AUTH_MANAGER] Updated cameras for server {server_ip}: {len(camera_ids)} cameras")
    
    def handle_token_expired(self, server_ip: str, operation: str = "API call"):
        """Handle when access token expires"""
        print(f"🚫 [AUTH_MANAGER] Access token expired for server {server_ip} during {operation}")
        logger.warning(f"🚫 [AUTH_MANAGER] Access token expired for server {server_ip} during {operation}")
        
        if server_ip in self._servers:
            self._servers[server_ip].auth_state = ServerAuthState.TOKEN_EXPIRED
            self._servers[server_ip].last_error = f"Token expired during {operation}"
    
    def handle_refresh_success(self, server_ip: str):
        """Handle successful token refresh"""
        print(f"✅ [AUTH_MANAGER] Token refresh successful for server: {server_ip}")
        logger.info(f"✅ [AUTH_MANAGER] Token refresh successful for server: {server_ip}")
        
        if server_ip in self._servers:
            self._servers[server_ip].auth_state = ServerAuthState.CONNECTED
            self._servers[server_ip].last_token_refresh = datetime.now()
            self._servers[server_ip].last_error = None
            
            # Notify cameras they can resume operations
            self._notify_cameras_authenticated(server_ip)
    
    def handle_refresh_failed(self, server_ip: str, reason: str = "Unknown"):
        """Handle failed token refresh - disconnect server"""
        print(f"❌ [AUTH_MANAGER] Token refresh failed for server {server_ip}: {reason}")
        logger.error(f"❌ [AUTH_MANAGER] Token refresh failed for server {server_ip}: {reason}")
        print(f"🔐 [AUTH_MANAGER] Please login again to server: {server_ip}")
        logger.error(f"🔐 [AUTH_MANAGER] Please login again to server: {server_ip}")
        
        if server_ip in self._servers:
            self._servers[server_ip].auth_state = ServerAuthState.REFRESH_FAILED
            self._servers[server_ip].last_error = reason
            
            # Disconnect server and update camera states
            self._disconnect_server(server_ip, reason)
    
    def _disconnect_server(self, server_ip: str, reason: str):
        """Disconnect server and update all related cameras"""
        print(f"🔌 [AUTH_MANAGER] Disconnecting server: {server_ip} - Reason: {reason}")
        logger.warning(f"🔌 [AUTH_MANAGER] Disconnecting server: {server_ip} - Reason: {reason}")
        
        if server_ip in self._servers:
            server_info = self._servers[server_ip]
            server_info.auth_state = ServerAuthState.DISCONNECTED
            
            # Update all cameras to unauthenticated state
            for camera_id in server_info.camera_ids:
                print(f"📹 [AUTH_MANAGER] Setting camera {camera_id} to unauthen state")
                logger.info(f"📹 [AUTH_MANAGER] Setting camera {camera_id} to unauthen state")
                self.camera_state_changed.emit(server_ip, camera_id, "unauthen")
            
            # Emit signals for UI components
            self.server_disconnected.emit(server_ip, reason)
            self.login_required.emit(server_ip, f"Authentication failed: {reason}")
            
            # Call notification callbacks
            for callback in self._notification_callbacks:
                try:
                    callback(server_ip, reason)
                except Exception as e:
                    logger.error(f"Error in notification callback: {e}")
    
    def _notify_cameras_authenticated(self, server_ip: str):
        """Notify cameras that server is authenticated again"""
        if server_ip in self._servers:
            server_info = self._servers[server_ip]
            for camera_id in server_info.camera_ids:
                print(f"📹 [AUTH_MANAGER] Camera {camera_id} can resume operations")
                logger.info(f"📹 [AUTH_MANAGER] Camera {camera_id} can resume operations")
                self.camera_state_changed.emit(server_ip, camera_id, "authenticated")
    
    def is_server_authenticated(self, server_ip: str) -> bool:
        """Check if server is authenticated"""
        if server_ip in self._servers:
            return self._servers[server_ip].auth_state == ServerAuthState.CONNECTED
        return False
    
    def get_server_state(self, server_ip: str) -> Optional[ServerAuthState]:
        """Get server authentication state"""
        if server_ip in self._servers:
            return self._servers[server_ip].auth_state
        return None
    
    def add_notification_callback(self, callback: Callable[[str, str], None]):
        """Add callback for server disconnection notifications"""
        self._notification_callbacks.append(callback)
    
    def remove_notification_callback(self, callback: Callable[[str, str], None]):
        """Remove notification callback"""
        if callback in self._notification_callbacks:
            self._notification_callbacks.remove(callback)
    
    def get_server_info(self, server_ip: str) -> Optional[ServerInfo]:
        """Get server information"""
        return self._servers.get(server_ip)
    
    def get_all_servers(self) -> Dict[str, ServerInfo]:
        """Get all registered servers"""
        return self._servers.copy()


# Global instance
auth_manager = AuthManager.get_instance()
