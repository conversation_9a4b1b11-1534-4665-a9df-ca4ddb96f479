"""
Enhanced Server Status Widget - Phase 4 UI Integration
Shows real-time server authentication status with token information
"""

import logging
from typing import Op<PERSON>
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtWidgets import QWidget, QHBoxLayout, QVBoxLayout, QLabel, QPushButton, QFrame
from PySide6.QtGui import QPixmap

from src.common.auth.simple_token_manager import SimpleTokenManager, ServerAuthState
from src.common.auth.auth_ui_manager import AuthUIManager
from src.common.controller.main_controller import main_controller
from src.styles.style import Style

logger = logging.getLogger(__name__)


class ServerStatusWidget(QWidget):
    """
    Enhanced server status widget with authentication state
    
    Features:
    1. ✅ Real-time server connection status
    2. ✅ Token expiration information
    3. ✅ Login required indicators
    4. ✅ Quick action buttons
    """
    
    # Signals
    login_requested = Signal(str)  # server_id
    refresh_requested = Signal(str)  # server_id
    
    def __init__(self, server_id: str, parent=None):
        super().__init__(parent)
        
        self.server_id = server_id
        self.token_manager = SimpleTokenManager.get_instance()
        self.auth_ui_manager = AuthUIManager.get_instance()
        
        # Setup UI
        self._setup_ui()
        
        # Setup update timer
        self._update_timer = QTimer()
        self._update_timer.timeout.connect(self._update_status)
        self._update_timer.start(10000)  # Update every 10 seconds
        
        # Connect to auth UI manager signals
        self.auth_ui_manager.server_status_changed.connect(self._on_server_status_changed)
        self.auth_ui_manager.login_required.connect(self._on_login_required)
        self.auth_ui_manager.token_refreshed.connect(self._on_token_refreshed)
        
        # Initial update
        self._update_status()
        
        logger.info(f"📊 ServerStatusWidget created for {server_id}")
    
    def _setup_ui(self):
        """Setup the UI components"""
        try:
            self.setFixedHeight(80)
            self.setStyleSheet("""
                ServerStatusWidget {
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    margin: 2px;
                }
            """)
            
            # Main layout
            main_layout = QHBoxLayout(self)
            main_layout.setContentsMargins(10, 8, 10, 8)
            main_layout.setSpacing(10)
            
            # Status icon
            self.status_icon = QLabel()
            self.status_icon.setFixedSize(16, 16)
            main_layout.addWidget(self.status_icon)
            
            # Server info layout
            info_layout = QVBoxLayout()
            info_layout.setSpacing(2)
            
            # Server name and status
            self.server_label = QLabel()
            self.server_label.setStyleSheet("""
                QLabel {
                    font-weight: bold;
                    font-size: 13px;
                    color: #212529;
                }
            """)
            info_layout.addWidget(self.server_label)
            
            # Status details
            self.status_label = QLabel()
            self.status_label.setStyleSheet("""
                QLabel {
                    font-size: 11px;
                    color: #6c757d;
                }
            """)
            info_layout.addWidget(self.status_label)
            
            main_layout.addLayout(info_layout)
            main_layout.addStretch()
            
            # Action buttons layout
            buttons_layout = QVBoxLayout()
            buttons_layout.setSpacing(2)
            
            # Login button
            self.login_button = QPushButton("Login")
            self.login_button.setFixedSize(60, 25)
            self.login_button.setStyleSheet("""
                QPushButton {
                    background-color: #007bff;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 10px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #0056b3;
                }
                QPushButton:disabled {
                    background-color: #6c757d;
                }
            """)
            self.login_button.clicked.connect(self._on_login_clicked)
            buttons_layout.addWidget(self.login_button)
            
            # Refresh button
            self.refresh_button = QPushButton("Refresh")
            self.refresh_button.setFixedSize(60, 25)
            self.refresh_button.setStyleSheet("""
                QPushButton {
                    background-color: #28a745;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 10px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #1e7e34;
                }
                QPushButton:disabled {
                    background-color: #6c757d;
                }
            """)
            self.refresh_button.clicked.connect(self._on_refresh_clicked)
            buttons_layout.addWidget(self.refresh_button)
            
            main_layout.addLayout(buttons_layout)
            
        except Exception as e:
            logger.error(f"❌ Failed to setup UI: {e}")
    
    def _update_status(self):
        """Update the status display"""
        try:
            # Get server info
            server_info = self.token_manager.get_all_servers().get(self.server_id)
            server_name = self._get_friendly_server_name()
            
            # Update server name
            self.server_label.setText(server_name)
            
            if not server_info:
                # No server info
                self._set_status_disconnected("Not configured")
                return
            
            # Update based on server state
            if server_info.auth_state == ServerAuthState.CONNECTED:
                self._set_status_connected()
            elif server_info.auth_state == ServerAuthState.REFRESHING:
                self._set_status_refreshing()
            else:
                reason = server_info.last_error or "Disconnected"
                self._set_status_disconnected(reason)
            
        except Exception as e:
            logger.error(f"❌ Failed to update status: {e}")
            self._set_status_disconnected("Error")
    
    def _set_status_connected(self):
        """Set UI to connected state"""
        try:
            # Status icon
            icon_path = main_controller.get_theme_attribute("Image", "icon_state_server_on")
            self.status_icon.setPixmap(QPixmap(icon_path).scaled(16, 16, Qt.KeepAspectRatio, Qt.SmoothTransformation))
            
            # Status text
            token_info = self.token_manager.get_token_info(self.server_id)
            if token_info and token_info.expires_at:
                time_left = token_info.time_until_expiry()
                if time_left:
                    hours = int(time_left.total_seconds() // 3600)
                    minutes = int((time_left.total_seconds() % 3600) // 60)
                    if hours > 0:
                        status_text = f"Connected • Session expires in {hours}h {minutes}m"
                    else:
                        status_text = f"Connected • Session expires in {minutes}m"
                    
                    # Warn if expires soon
                    if token_info.expires_soon():
                        status_text += " ⚠️"
                else:
                    status_text = "Connected • Session expired"
            else:
                status_text = "Connected • Active session"
            
            self.status_label.setText(status_text)
            self.status_label.setStyleSheet("""
                QLabel {
                    font-size: 11px;
                    color: #28a745;
                }
            """)
            
            # Button states
            self.login_button.setEnabled(False)
            self.login_button.setText("Connected")
            self.refresh_button.setEnabled(True)
            
        except Exception as e:
            logger.error(f"❌ Failed to set connected status: {e}")
    
    def _set_status_disconnected(self, reason: str):
        """Set UI to disconnected state"""
        try:
            # Status icon
            icon_path = main_controller.get_theme_attribute("Image", "icon_state_server_off")
            self.status_icon.setPixmap(QPixmap(icon_path).scaled(16, 16, Qt.KeepAspectRatio, Qt.SmoothTransformation))
            
            # Status text
            self.status_label.setText(f"Disconnected • {reason}")
            self.status_label.setStyleSheet("""
                QLabel {
                    font-size: 11px;
                    color: #dc3545;
                }
            """)
            
            # Button states
            self.login_button.setEnabled(True)
            self.login_button.setText("Login")
            self.refresh_button.setEnabled(False)
            
        except Exception as e:
            logger.error(f"❌ Failed to set disconnected status: {e}")
    
    def _set_status_refreshing(self):
        """Set UI to refreshing state"""
        try:
            # Status icon (use a neutral icon)
            icon_path = main_controller.get_theme_attribute("Image", "icon_state_server_on")
            self.status_icon.setPixmap(QPixmap(icon_path).scaled(16, 16, Qt.KeepAspectRatio, Qt.SmoothTransformation))
            
            # Status text
            self.status_label.setText("Refreshing session...")
            self.status_label.setStyleSheet("""
                QLabel {
                    font-size: 11px;
                    color: #ffc107;
                }
            """)
            
            # Button states
            self.login_button.setEnabled(False)
            self.refresh_button.setEnabled(False)
            
        except Exception as e:
            logger.error(f"❌ Failed to set refreshing status: {e}")
    
    def _get_friendly_server_name(self) -> str:
        """Get friendly server name"""
        try:
            from src.common.server.server_info import server_info_model_manager
            
            for server_info in server_info_model_manager.server_list.values():
                if f"{server_info.server_ip}:{server_info.server_port}" == self.server_id:
                    return server_info.server_name or server_info.server_ip
            
            return self.server_id.split(':')[0]
            
        except Exception:
            return self.server_id.split(':')[0]
    
    def _on_login_clicked(self):
        """Handle login button click"""
        try:
            logger.info(f"🔐 Login requested for server {self.server_id}")
            self.login_requested.emit(self.server_id)
            
        except Exception as e:
            logger.error(f"❌ Error handling login click: {e}")
    
    def _on_refresh_clicked(self):
        """Handle refresh button click"""
        try:
            logger.info(f"🔄 Refresh requested for server {self.server_id}")
            self.refresh_requested.emit(self.server_id)
            
            # Try to refresh token
            from src.common.controller.controller_manager import controller_manager
            controller = controller_manager.get_controller(server_ip=self.server_id.split(':')[0])
            
            if controller:
                success = self.token_manager.try_refresh_token(self.server_id, controller)
                if success:
                    self.auth_ui_manager.show_token_refresh_notification(self.server_id, True)
                else:
                    self.auth_ui_manager.show_token_refresh_notification(self.server_id, False)
            
        except Exception as e:
            logger.error(f"❌ Error handling refresh click: {e}")
    
    def _on_server_status_changed(self, server_id: str, status: str):
        """Handle server status change signal"""
        if server_id == self.server_id:
            self._update_status()
    
    def _on_login_required(self, server_id: str, reason: str):
        """Handle login required signal"""
        if server_id == self.server_id:
            self._update_status()
    
    def _on_token_refreshed(self, server_id: str):
        """Handle token refreshed signal"""
        if server_id == self.server_id:
            self._update_status()
    
    def update_auth_status(self, connected: bool, state: str):
        """Update authentication status (called by AuthUIManager)"""
        self._update_status()
    
    def cleanup(self):
        """Cleanup resources"""
        try:
            self._update_timer.stop()
            logger.info(f"🧹 ServerStatusWidget cleaned up for {self.server_id}")
            
        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")


class ServerStatusSummaryWidget(QWidget):
    """
    Summary widget showing overall server status
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.token_manager = SimpleTokenManager.get_instance()
        self.auth_ui_manager = AuthUIManager.get_instance()
        
        self._setup_ui()
        
        # Setup update timer
        self._update_timer = QTimer()
        self._update_timer.timeout.connect(self._update_summary)
        self._update_timer.start(15000)  # Update every 15 seconds
        
        # Connect to signals
        self.auth_ui_manager.server_status_changed.connect(self._update_summary)
        
        # Initial update
        self._update_summary()
        
        logger.info("📊 ServerStatusSummaryWidget created")
    
    def _setup_ui(self):
        """Setup the UI"""
        try:
            layout = QHBoxLayout(self)
            layout.setContentsMargins(10, 5, 10, 5)
            
            # Connected servers
            self.connected_label = QLabel()
            self.connected_label.setStyleSheet("""
                QLabel {
                    font-size: 12px;
                    color: #28a745;
                    font-weight: bold;
                }
            """)
            layout.addWidget(self.connected_label)
            
            # Separator
            separator = QFrame()
            separator.setFrameShape(QFrame.VLine)
            separator.setStyleSheet("color: #dee2e6;")
            layout.addWidget(separator)
            
            # Disconnected servers
            self.disconnected_label = QLabel()
            self.disconnected_label.setStyleSheet("""
                QLabel {
                    font-size: 12px;
                    color: #dc3545;
                    font-weight: bold;
                }
            """)
            layout.addWidget(self.disconnected_label)
            
            layout.addStretch()
            
        except Exception as e:
            logger.error(f"❌ Failed to setup summary UI: {e}")
    
    def _update_summary(self):
        """Update the summary display"""
        try:
            summary = self.auth_ui_manager.get_server_status_summary()
            
            # Update labels
            connected_icon = main_controller.get_theme_attribute("Image", "icon_state_server_on")
            disconnected_icon = main_controller.get_theme_attribute("Image", "icon_state_server_off")
            
            self.connected_label.setText(
                f'<img src="{connected_icon}" width="8" height="8" style="vertical-align:middle;"/> '
                f'Connected: {summary["connected_count"]}'
            )
            
            self.disconnected_label.setText(
                f'<img src="{disconnected_icon}" width="8" height="8" style="vertical-align:middle;"/> '
                f'Disconnected: {summary["disconnected_count"]}'
            )
            
        except Exception as e:
            logger.error(f"❌ Failed to update summary: {e}")
    
    def cleanup(self):
        """Cleanup resources"""
        try:
            self._update_timer.stop()
            logger.info("🧹 ServerStatusSummaryWidget cleaned up")
            
        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")
