"""
Test Enhanced API Client - Phase 2 Integration
Verify token expiration check and proactive refresh functionality
"""

import sys
import os
import logging
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

from src.api.enhanced_api_client import EnhancedAP<PERSON>lient, AuthenticationError
from src.common.auth.simple_token_manager import SimpleTokenManager, ServerAuthState
from src.common.server.server_info import ServerInfoModel, ServerInfo

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class MockServerInfoModel:
    """Mock ServerInfoModel for testing"""
    def __init__(self, server_ip="*************", server_port=8080, websocket_port=8081):
        self.data = Mock()
        self.data.server_ip = server_ip
        self.data.server_port = server_port
        self.data.websocket_port = websocket_port


class MockResponse:
    """Mock HTTP response"""
    def __init__(self, status_code=200, json_data=None):
        self.status_code = status_code
        self._json_data = json_data or {}
    
    def json(self):
        return self._json_data


def test_enhanced_login():
    """Test enhanced login with token storage"""
    print("\n" + "="*60)
    print("🧪 Testing Enhanced Login with Token Storage")
    print("="*60)
    
    # Clear token manager
    tm = SimpleTokenManager.get_instance()
    tm.clear_all_servers()
    
    # Create enhanced client
    server_info = MockServerInfoModel()
    
    with patch.object(EnhancedAPIClient, '__init__', lambda self, server: None):
        client = EnhancedAPIClient()
        client.server_ip = "*************"
        client.server_port = 8080
        client.server_url = "https://api.example.com"
        client.is_ip_address = False
        client.access_token = "test_access_token"
        client.refresh_token = "test_refresh_token"
        client.token_manager = tm
        client.server_id = "*************:8080"
    
    # Mock successful login response
    mock_response = MockResponse(200, {
        "data": {
            "accessToken": "test_access_token",
            "refreshToken": "test_refresh_token",
            "expires_in": 3600  # 1 hour
        }
    })
    
    with patch.object(client, 'login', return_value=mock_response) as mock_login:
        # Simulate login
        response = client.login("admin", "password")
        
        # Manually store tokens (since we mocked the login)
        tm.store_tokens(
            server_id=client.server_id,
            access_token="test_access_token",
            refresh_token="test_refresh_token",
            expires_in=3600
        )
    
    # Verify tokens stored
    assert tm.is_server_connected(client.server_id), "Server should be connected"
    token_info = tm.get_token_info(client.server_id)
    assert token_info is not None, "Token info should exist"
    assert token_info.expires_at is not None, "Token should have expiration"
    
    print("✅ Enhanced login with token storage works")


def test_proactive_refresh():
    """Test proactive refresh when token expires soon"""
    print("\n" + "="*60)
    print("🧪 Testing Proactive Refresh")
    print("="*60)
    
    tm = SimpleTokenManager.get_instance()
    server_id = "192.168.1.101:8080"
    
    # Store token that expires soon (2 minutes from now)
    expires_at = datetime.now() + timedelta(minutes=2)
    tm.store_tokens(
        server_id=server_id,
        access_token="old_access_token",
        refresh_token="old_refresh_token",
        expires_in=120  # 2 minutes
    )
    
    # Create mock client
    with patch.object(EnhancedAPIClient, '__init__', lambda self, server: None):
        client = EnhancedAPIClient()
        client.server_id = server_id
        client.token_manager = tm
        client.is_ip_address = False
        
        # Mock successful refresh
        def mock_refresh():
            client.access_token = "new_access_token"
            return "new_access_token"
        
        client.refresh_access_token = mock_refresh
    
    # Test ensure_valid_token - should trigger proactive refresh
    token_info = tm.get_token_info(server_id)
    print(f"Token expires at: {token_info.expires_at}")
    print(f"Token expires soon: {token_info.expires_soon()}")
    
    # This should trigger proactive refresh
    result = tm.ensure_valid_token(server_id, client)
    
    assert result == True, "Ensure valid token should succeed"
    assert client.access_token == "new_access_token", "Token should be refreshed"
    
    print("✅ Proactive refresh works")


def test_token_expiration_check():
    """Test token expiration check before API calls"""
    print("\n" + "="*60)
    print("🧪 Testing Token Expiration Check Before API Calls")
    print("="*60)
    
    tm = SimpleTokenManager.get_instance()
    server_id = "192.168.1.102:8080"
    
    # Store expired token
    expired_time = datetime.now() - timedelta(minutes=10)  # 10 minutes ago
    tm.store_tokens(
        server_id=server_id,
        access_token="expired_access_token",
        refresh_token="expired_refresh_token"
    )
    
    # Manually set expiration to past
    server_info = tm._servers[server_id]
    server_info.token.expires_at = expired_time
    
    # Create mock client
    with patch.object(EnhancedAPIClient, '__init__', lambda self, server: None):
        client = EnhancedAPIClient()
        client.server_id = server_id
        client.token_manager = tm
        client.is_ip_address = False
        
        # Mock failed refresh (token really expired)
        def mock_failed_refresh():
            return None
        
        client.refresh_access_token = mock_failed_refresh
    
    # Test _ensure_authenticated - should fail due to expired token
    result = client._ensure_authenticated()
    
    assert result == False, "Should fail authentication with expired token"
    assert not tm.is_server_connected(server_id), "Server should be disconnected"
    
    print("✅ Token expiration check works")


def test_api_call_with_proactive_refresh():
    """Test API call with automatic proactive refresh"""
    print("\n" + "="*60)
    print("🧪 Testing API Call with Proactive Refresh")
    print("="*60)
    
    tm = SimpleTokenManager.get_instance()
    server_id = "*************:8080"
    
    # Store token that expires soon
    tm.store_tokens(
        server_id=server_id,
        access_token="soon_expired_token",
        refresh_token="valid_refresh_token",
        expires_in=300  # 5 minutes - will trigger proactive refresh
    )
    
    # Create mock client
    with patch.object(EnhancedAPIClient, '__init__', lambda self, server: None):
        client = EnhancedAPIClient()
        client.server_id = server_id
        client.token_manager = tm
        client.is_ip_address = False
        
        # Mock successful refresh
        def mock_refresh():
            client.access_token = "refreshed_access_token"
            tm.update_access_token(server_id, "refreshed_access_token")
            return "refreshed_access_token"
        
        client.refresh_access_token = mock_refresh
        
        # Mock get_url and get_headers
        client.get_url = lambda endpoint: f"https://api.example.com{endpoint}"
        client.get_headers = lambda: {"Authorization": f"Bearer {client.access_token}"}
    
    # Mock successful API response
    mock_api_response = MockResponse(200, {"data": ["camera1", "camera2"]})
    
    with patch('requests.get', return_value=mock_api_response):
        # This should trigger proactive refresh before API call
        response = client.get_cameras()
    
    assert response is not None, "API call should succeed"
    assert response.status_code == 200, "Should get successful response"
    assert client.access_token == "refreshed_access_token", "Token should be refreshed"
    
    print("✅ API call with proactive refresh works")


def test_authentication_error_handling():
    """Test authentication error handling"""
    print("\n" + "="*60)
    print("🧪 Testing Authentication Error Handling")
    print("="*60)
    
    tm = SimpleTokenManager.get_instance()
    server_id = "*************:8080"
    
    # Don't store any tokens - server disconnected
    tm._disconnect_server(server_id, "Test disconnect")
    
    # Create mock client
    with patch.object(EnhancedAPIClient, '__init__', lambda self, server: None):
        client = EnhancedAPIClient()
        client.server_id = server_id
        client.token_manager = tm
        client.is_ip_address = False
        client.get_url = lambda endpoint: f"https://api.example.com{endpoint}"
    
    # Try API call - should raise AuthenticationError
    try:
        response = client.get_cameras()
        assert False, "Should have raised AuthenticationError"
    except AuthenticationError as e:
        assert "disconnected" in str(e).lower(), "Should mention disconnected"
        print("✅ Authentication error handling works")


def test_server_status_monitoring():
    """Test server status monitoring"""
    print("\n" + "="*60)
    print("🧪 Testing Server Status Monitoring")
    print("="*60)
    
    tm = SimpleTokenManager.get_instance()
    server_id = "*************:8080"
    
    # Store valid token
    tm.store_tokens(
        server_id=server_id,
        access_token="valid_token",
        refresh_token="valid_refresh",
        expires_in=3600
    )
    
    # Create mock client
    with patch.object(EnhancedAPIClient, '__init__', lambda self, server: None):
        client = EnhancedAPIClient()
        client.server_id = server_id
        client.token_manager = tm
    
    # Get server status
    status = client.get_server_status()
    
    assert status["server_id"] == server_id, "Should have correct server ID"
    assert status["connected"] == True, "Should be connected"
    assert status["state"] == "connected", "Should have connected state"
    assert status["token_info"] is not None, "Should have token info"
    
    print("✅ Server status monitoring works")
    print(f"📊 Status: {status}")


def main():
    """Run all tests"""
    print("🚀 Starting Enhanced API Client Tests - Phase 2")
    
    try:
        test_enhanced_login()
        test_proactive_refresh()
        test_token_expiration_check()
        test_api_call_with_proactive_refresh()
        test_authentication_error_handling()
        test_server_status_monitoring()
        
        print("\n" + "="*60)
        print("🎉 ALL PHASE 2 TESTS PASSED!")
        print("="*60)
        
        # Show final token manager state
        tm = SimpleTokenManager.get_instance()
        print(f"\n📊 Final Token Manager State:")
        print(f"   Total servers: {len(tm.get_all_servers())}")
        print(f"   Connected: {len(tm.get_connected_servers())}")
        print(f"   Connected servers: {tm.get_connected_servers()}")
        
        print("\n✅ Key Features Verified:")
        print("   🔍 Token expiration check before API calls")
        print("   ⏰ Proactive refresh when token expires soon")
        print("   🔌 Graceful disconnect when refresh fails")
        print("   📊 Server status monitoring")
        print("   🛡️ Authentication error handling")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
