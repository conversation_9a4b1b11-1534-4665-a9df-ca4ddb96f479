from src.common.widget.custom_titlebar.custom_component.login_title_bar import LoginTitleBar
from src.common.auth.auth_manager import auth_manager
from PySide6.QtCore import Qt, QTimer, Slot
from PySide6.QtGui import <PERSON><PERSON><PERSON><PERSON>pp<PERSON>,QShortcut,<PERSON><PERSON>ey<PERSON>e<PERSON>
from PySide6.QtWidgets import Q<PERSON>idget, QVBoxLayout, QHBoxLayout, QSizePolicy, QDialog, QProgressBar, QPushButton, QMessageBox
from src.presentation.server_screen.logo_widget import LogoWidget
from src.presentation.server_screen.list_server_widget import ListServerWidget
from src.presentation.server_screen.add_server_widget import AddServerWidget
from src.common.controller.main_controller import connect_slot,main_controller
import os
import src.utils.log_utils as LogUtils
import logging
logger = logging.getLogger(__name__)

basedir = os.path.dirname(__file__)

class ServerScreen(QWidget):
    def __init__(self, parent=None, window_parent=None):
        super().__init__()
        self.type_grid = 1
        self.window_parent = window_parent
        # self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.calculate_layout()
        layout = QVBoxLayout()
        layout.setContentsMargins(0,0,0,0)
        self.setLayout(layout)

        self.logo = LogoWidget(self)
        self.logo.setGeometry(0,0, self.screen_available_width,self.screen_available_height)

        self.servers = ListServerWidget()
        self.servers.change_logo_position.connect(self.change_logo_position)
        self.servers.load_qsetting_history()
        layout.addWidget(QWidget())
        layout.addWidget(self.servers)
        layout.setStretch(0,10)
        layout.setStretch(1,90)
        # Add server
        self.btn_add_server = AddServerWidget(self)
        self.btn_add_server.setGeometry(0,25,self.screen_available_width,80)

        self.title_bar = LoginTitleBar(parent=self, window_parent=self.window_parent)
        self.title_widget = QWidget(self)
        self.title_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        btn_test = QPushButton('Test')
        btn_test.clicked.connect(self.retranslate_server_screen)

        self.title_layout = QHBoxLayout(self.title_widget)
        self.title_layout.setAlignment(Qt.AlignmentFlag.AlignRight)
        self.title_layout.setContentsMargins(0, 0, 2, 0)
        # self.title_layout.addWidget(btn_test)
        self.title_layout.addWidget(self.title_bar)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.shortcut = QShortcut(QKeySequence(Qt.CTRL | Qt.Key_F), self)
        self.connect_slot()
        self.set_dynamic_stylesheet()

        # Connect to AuthManager for server disconnection notifications
        auth_manager.server_disconnected.connect(self.on_server_disconnected)
        auth_manager.login_required.connect(self.on_login_required)
        logger.info("🔗 [SERVER_SCREEN] Connected to AuthManager for server notifications")

    def connect_slot(self):
        connect_slot(
            (self.shortcut.activated,self.shortcut_activated))
        
    def shortcut_activated(self):
        # logger.debug(f'shortcut_activated')
        self.btn_add_server.btn_search_server_clicked()

    def change_logo_position(self,data):
        self.type_grid=data
        if data == 1:
            self.logo.setGeometry(0,0, self.screen_available_width ,self.screen_available_height - 70)
        elif data == 2:
            self.logo.setGeometry(0,0, self.screen_available_width ,self.screen_available_height - 70)

    def calculate_layout(self, desktop_screen_size=None):
        if desktop_screen_size is None:
            screen = QGuiApplication.primaryScreen()
            desktop_screen_size = screen.availableGeometry()
        screen_width = desktop_screen_size.width()
        screen_height = desktop_screen_size.height()
        percent_menu_bar = 0.03
        self.screen_available_width = screen_width
        self.screen_available_height = screen_height

    def retranslate_server_screen(self):
        self.servers.retranslate_ui_server()
        self.btn_add_server.retranslate_ui_server()
    def resizeEvent(self, event):
        frame_size = self.geometry()
        self.title_widget.setGeometry(0, 0, self.width(), 32)
        self.screen_available_width = self.width()
        self.screen_available_height = self.height()
        
        self.btn_add_server.setGeometry(0,25,self.screen_available_width,80)
        self.change_logo_position(self.type_grid)
        
    def window_state_changed(self, state):
        if state == Qt.WindowState.WindowFullScreen:
            self.title_bar.stacked_button.setCurrentIndex(1)
        else:
            self.title_bar.stacked_button.setCurrentIndex(0)

    def restyle_ui_server_screen(self):
        self.set_dynamic_stylesheet()
        self.title_bar.set_dynamic_stylesheet()

    def set_dynamic_stylesheet(self):
        self.logo.set_dynamic_stylesheet()
        self.servers.set_dynamic_stylesheet()
        self.btn_add_server.set_dynamic_stylesheet()
        # self.title_bar.set_dynamic_stylesheet()

    @Slot(str, str)
    def on_server_disconnected(self, server_ip: str, reason: str):
        """Handle server disconnection notifications from AuthManager"""
        print(f"🔌 [SERVER_SCREEN] Server disconnected: {server_ip} - Reason: {reason}")
        logger.warning(f"🔌 [SERVER_SCREEN] Server disconnected: {server_ip} - Reason: {reason}")

        # Update UI to reflect server disconnection
        # This could involve updating server status indicators, etc.

    @Slot(str, str)
    def on_login_required(self, server_ip: str, reason: str):
        """Handle login required notifications from AuthManager"""
        print(f"🔐 [SERVER_SCREEN] Login required for server: {server_ip} - Reason: {reason}")
        logger.warning(f"🔐 [SERVER_SCREEN] Login required for server: {server_ip} - Reason: {reason}")

        # Show user-friendly notification
        self._show_login_required_notification(server_ip, reason)

    def _show_login_required_notification(self, server_ip: str, reason: str):
        """Show login required notification to user"""
        try:
            # Create user-friendly message
            title = "Authentication Required"
            message = f"Your session has expired for server {server_ip}.\n\nReason: {reason}\n\nPlease login again to continue."

            # Show message box
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle(title)
            msg_box.setText(message)
            msg_box.setIcon(QMessageBox.Warning)
            msg_box.setStandardButtons(QMessageBox.Ok)
            msg_box.exec()

            print(f"📢 [SERVER_SCREEN] Login required notification shown for {server_ip}")
            logger.info(f"📢 [SERVER_SCREEN] Login required notification shown for {server_ip}")

        except Exception as e:
            print(f"❌ [SERVER_SCREEN] Failed to show login notification: {e}")
            logger.error(f"❌ [SERVER_SCREEN] Failed to show login notification: {e}")