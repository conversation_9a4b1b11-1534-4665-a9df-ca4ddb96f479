"""
Enhanced WebSocket Client with SimpleTokenManager Integration
Phase 3: Solve WebSocket reconnection and token coordination issues
"""

import logging
import time
import threading
from typing import Optional
from http import HTTPStatus
from websocket._exceptions import WebSocketBadStatusException

from .websocket_client import Webso<PERSON><PERSON>lient
from src.common.auth.simple_token_manager import SimpleTokenManager, ServerAuthState
from src.common.controller.controller_manager import controller_manager

logger = logging.getLogger(__name__)


class EnhancedWebsocketClient(WebsocketClient):
    """
    Enhanced WebSocket Client with proactive token management
    
    Key Features:
    1. ✅ Stop reconnecting when server disconnected
    2. ✅ Coordinate with SimpleTokenManager for token state
    3. ✅ Update headers when token refreshed
    4. ✅ Graceful handling of authentication failures
    """
    
    def __init__(self, url: str, header: Optional[dict] = None, server_ip: Optional[str] = None):
        # Initialize parent WebSocket client
        super().__init__(url, header, server_ip)
        
        # ADD: Token manager integration
        self.token_manager = SimpleTokenManager.get_instance()
        self.server_id = self._generate_server_id()
        self._reconnect_enabled = True
        self._connection_attempts = 0
        self._max_connection_attempts = 3
        
        logger.info(f"🌐 EnhancedWebsocketClient created for server {self.server_id}")
    
    def _generate_server_id(self) -> str:
        """Generate server ID consistent with APIClient"""
        try:
            if self.server_ip:
                # Assume standard port 8080 for API, could be configurable
                return f"{self.server_ip}:8080"
            return "unknown_server:8080"
        except Exception as e:
            logger.warning(f"⚠️ Failed to generate WebSocket server ID: {e}")
            return "unknown_server:8080"
    
    def connect(self):
        """Enhanced connect with server state checking"""
        # Check if server is connected before attempting WebSocket connection
        if not self.token_manager.is_server_connected(self.server_id):
            logger.warning(f"⚠️ Server {self.server_id} not authenticated, skipping WebSocket connection")
            return
        
        # Update headers with current token before connecting
        self._update_auth_headers()
        
        logger.info(f"🔗 Connecting WebSocket to {self.server_id}")
        self._connection_attempts += 1
        
        try:
            super().connect()
        except Exception as e:
            logger.error(f"❌ WebSocket connection failed for {self.server_id}: {e}")
    
    def connect_background(self):
        """Enhanced background connect with state checking"""
        if not self.token_manager.is_server_connected(self.server_id):
            logger.warning(f"⚠️ Server {self.server_id} not authenticated, not starting WebSocket")
            return
        
        super().connect_background()
    
    def _update_auth_headers(self):
        """Update WebSocket headers with current access token"""
        access_token = self.token_manager.get_access_token(self.server_id)
        if access_token:
            self.header['Authorization'] = f"Bearer {access_token}"
            logger.debug(f"🔑 Updated WebSocket headers for {self.server_id}")
        else:
            logger.warning(f"⚠️ No access token available for {self.server_id}")
    
    def on_open(self, ws):
        """Enhanced on_open with connection tracking"""
        logger.info(f"✅ WebSocket connected to {self.server_id}")
        self._connection_attempts = 0  # Reset connection attempts on success
        super().on_open(ws)
    
    def on_error(self, ws, error: Exception):
        """
        Enhanced error handling with SimpleTokenManager coordination
        KEY IMPROVEMENT: Stop trying to refresh if server is disconnected
        """
        logger.warning(f"🚫 WebSocket error for {self.server_id}: {error}")
        
        # Call parent error handling first
        super().on_error(ws, error)
        
        if isinstance(error, WebSocketBadStatusException):
            if error.status_code == HTTPStatus.UNAUTHORIZED:
                logger.warning(f"🔐 WebSocket 401 Unauthorized for {self.server_id}")
                
                # ✅ KEY IMPROVEMENT: Check server state before attempting refresh
                if self.token_manager.is_server_connected(self.server_id):
                    logger.info(f"🔄 Server connected, attempting token refresh for WebSocket")
                    
                    # Try to refresh token through token manager
                    controller = controller_manager.get_controller(server_ip=self.server_ip)
                    if controller and self.token_manager.try_refresh_token(self.server_id, controller):
                        # Refresh successful - update headers
                        self._update_auth_headers()
                        logger.info(f"✅ Token refreshed for WebSocket {self.server_id}")
                    else:
                        # Refresh failed - server will be disconnected by token manager
                        logger.warning(f"❌ Token refresh failed for WebSocket {self.server_id}")
                        self._disable_reconnect("Token refresh failed")
                else:
                    # Server already disconnected - don't try to refresh
                    logger.warning(f"⚠️ Server {self.server_id} disconnected, not attempting WebSocket refresh")
                    self._disable_reconnect("Server disconnected")
            else:
                # Other HTTP errors
                logger.error(f"❌ WebSocket HTTP error {error.status_code} for {self.server_id}")
                if self._connection_attempts >= self._max_connection_attempts:
                    self._disable_reconnect(f"Max connection attempts reached: {error}")
        else:
            # Network or other errors
            logger.error(f"❌ WebSocket network error for {self.server_id}: {error}")
            if self._connection_attempts >= self._max_connection_attempts:
                self._disable_reconnect(f"Max connection attempts reached: {error}")
    
    def on_close(self, ws, close_status_code, close_msg):
        """
        Enhanced close handling - KEY IMPROVEMENT: Don't reconnect if server disconnected
        """
        logger.info(f"🔌 WebSocket closed for {self.server_id}: {close_status_code} - {close_msg}")
        
        # Call parent close handling first (but override reconnect logic)
        message = f"close_status_code = {close_status_code} close_msg = {close_msg}"
        data = {"message": message}
        from .event_type import EventType
        event = EventType.connection_lost
        message_json = {'event': event, 'data': data}
        self._WebsocketClient__on_websocket_event(message_json)
        
        # ✅ KEY IMPROVEMENT: Only reconnect if server is still connected
        if self._should_reconnect():
            logger.info(f"🔄 Server connected, attempting WebSocket reconnect for {self.server_id}")
            time.sleep(1)
            self.connect_background()
        else:
            logger.info(f"⚠️ Not reconnecting WebSocket for {self.server_id} - server disconnected or disabled")
    
    def _should_reconnect(self) -> bool:
        """Determine if WebSocket should attempt to reconnect"""
        # Don't reconnect if explicitly disabled
        if not self._reconnect_enabled:
            return False
        
        # Don't reconnect if server is disconnected
        if not self.token_manager.is_server_connected(self.server_id):
            logger.info(f"📊 Server {self.server_id} state: {self.token_manager.get_server_state(self.server_id)}")
            return False
        
        # Don't reconnect if too many attempts
        if self._connection_attempts >= self._max_connection_attempts:
            return False
        
        return True
    
    def _disable_reconnect(self, reason: str):
        """Disable automatic reconnection"""
        logger.warning(f"🚫 Disabling WebSocket reconnect for {self.server_id}: {reason}")
        self._reconnect_enabled = False
    
    def enable_reconnect(self):
        """Re-enable automatic reconnection (e.g., after successful login)"""
        logger.info(f"✅ Enabling WebSocket reconnect for {self.server_id}")
        self._reconnect_enabled = True
        self._connection_attempts = 0
    
    def refresh_token(self) -> bool:
        """
        Enhanced token refresh using SimpleTokenManager
        OVERRIDE parent method to use token manager
        """
        logger.info(f"🔄 Enhanced token refresh for WebSocket {self.server_id}")
        
        try:
            # Get controller for this server
            controller = controller_manager.get_controller(server_ip=self.server_ip)
            if not controller:
                logger.error(f"❌ No controller found for server {self.server_ip}")
                return False
            
            # Use token manager for refresh
            if self.token_manager.try_refresh_token(self.server_id, controller):
                # Update WebSocket headers with new token
                self._update_auth_headers()
                
                # Notify map model if needed (keep existing functionality)
                from src.common.qml.models.map_controller import map_manager
                map_model = map_manager.get_map_model(serverIp=self.server_ip)
                if map_model:
                    map_model.accessTokenChanged.emit()
                
                logger.info(f"✅ WebSocket token refresh successful for {self.server_id}")
                return True
            else:
                logger.warning(f"❌ WebSocket token refresh failed for {self.server_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ WebSocket token refresh error for {self.server_id}: {e}")
            return False
    
    def get_connection_status(self) -> dict:
        """Get WebSocket connection status for monitoring"""
        return {
            "server_id": self.server_id,
            "server_connected": self.token_manager.is_server_connected(self.server_id),
            "server_state": self.token_manager.get_server_state(self.server_id).value,
            "reconnect_enabled": self._reconnect_enabled,
            "connection_attempts": self._connection_attempts,
            "max_attempts": self._max_connection_attempts,
            "websocket_url": self.url,
            "has_auth_header": 'Authorization' in self.header
        }
    
    def force_reconnect(self):
        """Force WebSocket reconnection (e.g., after successful login)"""
        logger.info(f"🔄 Force reconnecting WebSocket for {self.server_id}")
        
        # Re-enable reconnection
        self.enable_reconnect()
        
        # Update headers
        self._update_auth_headers()
        
        # Close current connection and reconnect
        try:
            self.close()
            time.sleep(0.5)
            self.connect_background()
        except Exception as e:
            logger.error(f"❌ Force reconnect failed for {self.server_id}: {e}")
    
    def disconnect_gracefully(self, reason: str = "Manual disconnect"):
        """Gracefully disconnect WebSocket"""
        logger.info(f"🔌 Gracefully disconnecting WebSocket for {self.server_id}: {reason}")
        
        # Disable reconnection
        self._disable_reconnect(reason)
        
        # Close connection
        try:
            self.close()
        except Exception as e:
            logger.error(f"❌ Error during graceful disconnect: {e}")
    
    def __str__(self):
        """String representation for debugging"""
        status = self.get_connection_status()
        return f"EnhancedWebsocketClient(server={self.server_id}, connected={status['server_connected']}, reconnect={status['reconnect_enabled']})"
