#!/usr/bin/env python3
"""
Test script for token management features

This script tests the new token management functionality:
1. Logging khi access_token hết hạn
2. Refresh token thành công  
3. Thông báo khi refresh_token hết hạn
4. Camera state chuyển thành unauthen
5. API calls bị disable khi không có token
6. Tự động recovery khi có token mới
"""

import sys
import os
import logging
from unittest.mock import Mock, patch, MagicMock
import requests

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_auth_manager():
    """Test AuthManager functionality"""
    print("\n" + "="*60)
    print("🧪 TESTING: AuthManager")
    print("="*60)
    
    try:
        from src.common.auth.auth_manager import AuthManager, ServerAuthState
        
        # Create AuthManager instance
        auth_manager = AuthManager()
        
        # Test 1: Register server
        print("\n1️⃣ Testing server registration...")
        server_ip = "*************"
        server_url = "http://*************:8080"
        camera_ids = ["cam1", "cam2", "cam3"]
        
        auth_manager.register_server(server_ip, server_url, camera_ids)
        
        # Verify server is registered
        server_info = auth_manager.get_server_info(server_ip)
        assert server_info is not None, "Server should be registered"
        assert server_info.auth_state == ServerAuthState.CONNECTED, "Server should be connected"
        print("✅ Server registration successful")
        
        # Test 2: Handle token expiration
        print("\n2️⃣ Testing token expiration handling...")
        auth_manager.handle_token_expired(server_ip, "get_cameras")
        
        server_info = auth_manager.get_server_info(server_ip)
        assert server_info.auth_state == ServerAuthState.TOKEN_EXPIRED, "Server should be in token expired state"
        print("✅ Token expiration handling successful")
        
        # Test 3: Handle refresh success
        print("\n3️⃣ Testing refresh success...")
        auth_manager.handle_refresh_success(server_ip)
        
        server_info = auth_manager.get_server_info(server_ip)
        assert server_info.auth_state == ServerAuthState.CONNECTED, "Server should be connected after refresh"
        print("✅ Refresh success handling successful")
        
        # Test 4: Handle refresh failure
        print("\n4️⃣ Testing refresh failure...")
        auth_manager.handle_refresh_failed(server_ip, "Refresh token expired")
        
        server_info = auth_manager.get_server_info(server_ip)
        assert server_info.auth_state == ServerAuthState.DISCONNECTED, "Server should be disconnected after refresh failure"
        print("✅ Refresh failure handling successful")
        
        # Test 5: Check authentication status
        print("\n5️⃣ Testing authentication status check...")
        is_authenticated = auth_manager.is_server_authenticated(server_ip)
        assert not is_authenticated, "Server should not be authenticated after disconnect"
        print("✅ Authentication status check successful")
        
        print("\n🎉 AuthManager tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ AuthManager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_client_token_handling():
    """Test APIClient token handling"""
    print("\n" + "="*60)
    print("🧪 TESTING: APIClient Token Handling")
    print("="*60)
    
    try:
        from src.api.api_client import APIClient
        from src.common.server.server_info import ServerInfoModel, ServerData
        
        # Create mock server
        server_data = ServerData()
        server_data.server_ip = "*************"
        server_data.server_port = 8080
        server_data.websocket_port = 8081
        
        server_info = ServerInfoModel()
        server_info.data = server_data
        
        # Create APIClient
        api_client = APIClient(server_info)
        
        # Test 1: Test _handle_api_response with 401
        print("\n1️⃣ Testing 401 response handling...")
        
        # Mock response with 401
        mock_response = Mock()
        mock_response.status_code = 401
        
        # Mock refresh_access_token to return None (failure)
        with patch.object(api_client, 'refresh_access_token', return_value=None):
            result = api_client._handle_api_response(mock_response, "test_operation")
            assert not result, "Should return False for failed authentication"
        
        print("✅ 401 response handling successful")
        
        # Test 2: Test successful response
        print("\n2️⃣ Testing successful response...")
        mock_response.status_code = 200
        result = api_client._handle_api_response(mock_response, "test_operation")
        assert result, "Should return True for successful response"
        print("✅ Successful response handling successful")
        
        print("\n🎉 APIClient token handling tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ APIClient test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_websocket_token_handling():
    """Test WebSocketClient token handling"""
    print("\n" + "="*60)
    print("🧪 TESTING: WebSocketClient Token Handling")
    print("="*60)
    
    try:
        from src.common.websocket.websocket_client import WebsocketClient
        from websocket._exceptions import WebSocketBadStatusException
        from http import HTTPStatus
        
        # Create WebSocketClient
        ws_client = WebsocketClient("ws://*************:8081/socket", server_ip="*************")
        
        # Test 1: Test 401 error handling
        print("\n1️⃣ Testing 401 error handling...")
        
        # Mock refresh_token to return False (failure)
        with patch.object(ws_client, 'refresh_token', return_value=False):
            with patch.object(ws_client, '_notify_server_disconnected') as mock_notify:
                # Create 401 error
                error = WebSocketBadStatusException("Unauthorized", 401, None)
                
                # Call on_error
                ws_client.on_error(None, error)
                
                # Verify notification was called
                mock_notify.assert_called_once_with("Token refresh failed")
        
        print("✅ 401 error handling successful")
        
        print("\n🎉 WebSocketClient token handling tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ WebSocketClient test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_camera_grid_item_auth():
    """Test CameraGridItem authentication handling"""
    print("\n" + "="*60)
    print("🧪 TESTING: CameraGridItem Authentication")
    print("="*60)
    
    try:
        # This test would require more complex setup with Qt
        # For now, just verify the module can be imported
        from src.common.qml.models.camera_grid_item import CameraGridItem
        
        print("✅ CameraGridItem module imported successfully")
        print("📝 Note: Full CameraGridItem testing requires Qt environment")
        
        return True
        
    except Exception as e:
        print(f"❌ CameraGridItem test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Token Management Tests")
    print("="*80)
    
    tests = [
        ("AuthManager", test_auth_manager),
        ("APIClient Token Handling", test_api_client_token_handling),
        ("WebSocketClient Token Handling", test_websocket_token_handling),
        ("CameraGridItem Authentication", test_camera_grid_item_auth),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*80)
    print("📊 TEST SUMMARY")
    print("="*80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("⚠️ Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
