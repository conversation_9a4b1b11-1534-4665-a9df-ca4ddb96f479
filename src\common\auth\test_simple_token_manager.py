"""
Simple test for SimpleTokenManager
Run this to verify the implementation works correctly
"""

import sys
import os
import logging

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from src.common.auth.simple_token_manager import SimpleTokenManager, ServerAuthState

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class MockAPIClient:
    """Mock API client for testing"""
    def __init__(self, should_refresh_succeed=True):
        self.should_refresh_succeed = should_refresh_succeed
        self.access_token = "new_access_token_123"
        self.refresh_token = "new_refresh_token_123"
    
    def refresh_access_token(self):
        """Mock refresh method"""
        if self.should_refresh_succeed:
            logger.info("Mock: Refresh successful")
            return self.access_token
        else:
            logger.info("Mock: Refresh failed")
            return None


def test_basic_functionality():
    """Test basic token manager functionality"""
    print("\n" + "="*50)
    print("🧪 Testing Basic Functionality")
    print("="*50)
    
    # Get singleton instance
    tm = SimpleTokenManager.get_instance()
    print(f"✅ Token manager created: {tm}")
    
    # Test singleton pattern
    tm2 = SimpleTokenManager.get_instance()
    assert tm is tm2, "Singleton pattern failed"
    print("✅ Singleton pattern works")
    
    # Store tokens
    server_id = "*************:8080"
    tm.store_tokens(
        server_id=server_id,
        access_token="access_token_123",
        refresh_token="refresh_token_123",
        server_url="http://*************:8080"
    )
    
    # Check server state
    assert tm.is_server_connected(server_id), "Server should be connected"
    assert tm.get_server_state(server_id) == ServerAuthState.CONNECTED
    print("✅ Token storage and state check works")
    
    # Get tokens
    access_token = tm.get_access_token(server_id)
    refresh_token = tm.get_refresh_token(server_id)
    assert access_token == "access_token_123"
    assert refresh_token == "refresh_token_123"
    print("✅ Token retrieval works")


def test_successful_refresh():
    """Test successful token refresh"""
    print("\n" + "="*50)
    print("🧪 Testing Successful Token Refresh")
    print("="*50)
    
    tm = SimpleTokenManager.get_instance()
    server_id = "*************:8080"
    
    # Store initial tokens
    tm.store_tokens(
        server_id=server_id,
        access_token="old_access_token",
        refresh_token="old_refresh_token"
    )
    
    # Mock successful refresh
    mock_client = MockAPIClient(should_refresh_succeed=True)
    result = tm.try_refresh_token(server_id, mock_client)
    
    assert result == True, "Refresh should succeed"
    assert tm.is_server_connected(server_id), "Server should remain connected"
    assert tm.get_access_token(server_id) == "new_access_token_123"
    print("✅ Successful token refresh works")


def test_failed_refresh():
    """Test failed token refresh - should disconnect"""
    print("\n" + "="*50)
    print("🧪 Testing Failed Token Refresh (Disconnect)")
    print("="*50)
    
    tm = SimpleTokenManager.get_instance()
    server_id = "192.168.1.102:8080"
    
    # Store initial tokens
    tm.store_tokens(
        server_id=server_id,
        access_token="old_access_token",
        refresh_token="old_refresh_token"
    )
    
    # Verify initially connected
    assert tm.is_server_connected(server_id), "Server should be initially connected"
    
    # Mock failed refresh
    mock_client = MockAPIClient(should_refresh_succeed=False)
    result = tm.try_refresh_token(server_id, mock_client)
    
    assert result == False, "Refresh should fail"
    assert not tm.is_server_connected(server_id), "Server should be disconnected"
    assert tm.get_server_state(server_id) == ServerAuthState.DISCONNECTED
    assert tm.get_server_error(server_id) is not None, "Should have error message"
    print("✅ Failed token refresh disconnects server correctly")


def test_multiple_servers():
    """Test multiple server management"""
    print("\n" + "="*50)
    print("🧪 Testing Multiple Server Management")
    print("="*50)
    
    tm = SimpleTokenManager.get_instance()
    
    # Clear previous tests
    tm.clear_all_servers()
    
    # Add multiple servers
    servers = [
        "*************:8080",
        "*************:8080", 
        "api.example.com:443"
    ]
    
    for i, server_id in enumerate(servers):
        tm.store_tokens(
            server_id=server_id,
            access_token=f"access_token_{i}",
            refresh_token=f"refresh_token_{i}"
        )
    
    # Check all connected
    connected_servers = tm.get_connected_servers()
    assert len(connected_servers) == 3, f"Should have 3 connected servers, got {len(connected_servers)}"
    print("✅ Multiple server storage works")
    
    # Disconnect one server
    tm.disconnect_server(servers[1], "Test disconnect")
    
    # Check states
    assert tm.is_server_connected(servers[0]), "Server 0 should remain connected"
    assert not tm.is_server_connected(servers[1]), "Server 1 should be disconnected"
    assert tm.is_server_connected(servers[2]), "Server 2 should remain connected"
    
    connected_servers = tm.get_connected_servers()
    assert len(connected_servers) == 2, f"Should have 2 connected servers, got {len(connected_servers)}"
    print("✅ Independent server state management works")


def test_edge_cases():
    """Test edge cases and error handling"""
    print("\n" + "="*50)
    print("🧪 Testing Edge Cases")
    print("="*50)
    
    tm = SimpleTokenManager.get_instance()
    
    # Test non-existent server
    assert not tm.is_server_connected("non_existent_server")
    assert tm.get_access_token("non_existent_server") is None
    assert tm.get_server_state("non_existent_server") == ServerAuthState.DISCONNECTED
    print("✅ Non-existent server handling works")
    
    # Test refresh without tokens
    mock_client = MockAPIClient()
    result = tm.try_refresh_token("non_existent_server", mock_client)
    assert result == False, "Refresh should fail for non-existent server"
    print("✅ Refresh without tokens handled correctly")


def main():
    """Run all tests"""
    print("🚀 Starting SimpleTokenManager Tests")
    
    try:
        test_basic_functionality()
        test_successful_refresh()
        test_failed_refresh()
        test_multiple_servers()
        test_edge_cases()
        
        print("\n" + "="*50)
        print("🎉 ALL TESTS PASSED!")
        print("="*50)
        
        # Show final state
        tm = SimpleTokenManager.get_instance()
        print(f"\nFinal state: {tm}")
        print(f"Connected servers: {tm.get_connected_servers()}")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
