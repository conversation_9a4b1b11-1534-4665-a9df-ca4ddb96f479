# OAuth 2.0 Integration Guide - How to Run

## 🚀 **QUICK START - 3 STEPS**

### **Step 1: Test Core Functionality**
```bash
# Test Phase 1 - Core Token Manager
cd c:\Users\<USER>\work2025\iVMS
python src/common/auth/test_simple_token_manager.py

# Expected Output: "🎉 ALL TESTS PASSED!"
```

### **Step 2: Test Integration Demos**
```bash
# Test Phase 2 - API Integration Demo
python src/api/phase2_demo.py

# Test Phase 3 - WebSocket Demo  
python src/common/websocket/phase3_demo.py

# Test Phase 4 - UI Demo
python src/common/auth/phase4_demo.py
```

### **Step 3: Integrate with Existing Code**
See integration examples below ⬇️

## 🔧 **INTEGRATION WITH EXISTING iVMS CODE**

### **Option A: Gradual Integration (RECOMMENDED)**

#### **1. Replace APIClient with EnhancedAPIClient**
```python
# In existing controller code
# OLD:
from src.api.api_client import APIClient
api_client = APIClient(server_info)

# NEW:
from src.api.enhanced_api_client import EnhancedAPIClient
api_client = EnhancedAPIClient(server_info)

# ✅ All existing methods work the same
# ✅ Automatic token management added
# ✅ Proactive refresh enabled
```

#### **2. Replace WebSocketClient with EnhancedWebsocketClient**
```python
# In existing WebSocket code
# OLD:
from src.common.websocket.websocket_client import WebsocketClient
ws_client = WebsocketClient(url, headers, server_ip)

# NEW:
from src.common.websocket.enhanced_websocket_client import EnhancedWebsocketClient
ws_client = EnhancedWebsocketClient(url, headers, server_ip)

# ✅ All existing methods work the same
# ✅ Smart reconnection added
# ✅ Token coordination enabled
```

#### **3. Add UI Notifications (Optional)**
```python
# In main window initialization
from src.common.auth.auth_ui_manager import AuthUIManager

# Initialize UI manager
auth_ui = AuthUIManager.get_instance()
auth_ui.set_notification_parent(self)  # self = main window

# Connect to signals for custom handling
auth_ui.login_required.connect(self.show_login_dialog)
auth_ui.server_status_changed.connect(self.update_server_list)
```

### **Option B: Full Integration (ADVANCED)**

#### **1. Enhanced Login Dialog**
```python
# Replace existing login dialog
# OLD:
from src.presentation.server_screen.login_dialog import LoginDialog
login_dialog = LoginDialog(parent, controller)

# NEW:
from src.presentation.server_screen.enhanced_login_dialog import EnhancedLoginDialog
login_dialog = EnhancedLoginDialog(parent, controller)

# ✅ Better user feedback
# ✅ Token status display
# ✅ Automatic token storage
```

#### **2. Server Status Widgets**
```python
# Add to server management UI
from src.common.widget.server_status_widget import ServerStatusWidget, ServerStatusSummaryWidget

# For each server
for server_id in server_list:
    status_widget = ServerStatusWidget(server_id, parent=server_panel)
    status_widget.login_requested.connect(self.show_login_dialog)
    status_widget.refresh_requested.connect(self.refresh_server)
    
    # Add to layout
    server_layout.addWidget(status_widget)

# Add summary widget to status bar
summary_widget = ServerStatusSummaryWidget(parent=status_bar)
status_bar_layout.addWidget(summary_widget)
```

## 📋 **SPECIFIC INTEGRATION POINTS**

### **1. Controller Integration**
```python
# In src/common/controller/controller.py
class Controller:
    def __init__(self, server: ServerInfoModel):
        # OLD: 
        # self.api_client = APIClient(server)
        
        # NEW:
        from src.api.enhanced_api_client import EnhancedAPIClient
        self.api_client = EnhancedAPIClient(server)
        
        # ✅ All existing controller methods work unchanged
        # ✅ Automatic token management added
    
    def login(self, parent=None):
        """Enhanced login with better error handling"""
        try:
            # Existing login logic works the same
            response = self.api_client.login(username, password, captcha, captcha_id)
            
            # ✅ Tokens automatically stored in SimpleTokenManager
            # ✅ UI notifications automatically shown
            
            return response
            
        except AuthenticationError as e:
            # ✅ Clear error handling
            logger.error(f"Authentication failed: {e}")
            # Show user-friendly error message
            return None
```

### **2. WebSocket Integration**
```python
# In WebSocket initialization code
class SomeWebSocketHandler:
    def __init__(self, server_ip):
        # OLD:
        # self.ws_client = WebsocketClient(url, headers, server_ip)
        
        # NEW:
        from src.common.websocket.enhanced_websocket_client import EnhancedWebsocketClient
        self.ws_client = EnhancedWebsocketClient(url, headers, server_ip)
        
        # ✅ Existing WebSocket code works unchanged
        # ✅ Smart reconnection added
        # ✅ Token coordination enabled
    
    def connect(self):
        # ✅ Automatically checks server authentication state
        # ✅ Only connects if server is authenticated
        self.ws_client.connect_background()
```

### **3. Main Window Integration**
```python
# In src/presentation/main_screen/main_screen.py
class MainScreen(QMainWindow):
    def __init__(self):
        super().__init__()
        
        # ADD: Initialize auth UI manager
        from src.common.auth.auth_ui_manager import AuthUIManager
        self.auth_ui = AuthUIManager.get_instance()
        self.auth_ui.set_notification_parent(self)
        
        # Connect to authentication events
        self.auth_ui.login_required.connect(self.on_login_required)
        self.auth_ui.server_status_changed.connect(self.on_server_status_changed)
        
        # Existing initialization code...
        self.setup_ui()
    
    def on_login_required(self, server_id: str, reason: str):
        """Handle login required notification"""
        # Show login dialog for specific server
        self.show_login_dialog_for_server(server_id)
    
    def on_server_status_changed(self, server_id: str, status: str):
        """Handle server status change"""
        # Update UI indicators
        self.update_server_indicators(server_id, status)
```

## 🧪 **TESTING YOUR INTEGRATION**

### **1. Basic Functionality Test**
```python
# Create test script: test_integration.py
from src.common.auth.simple_token_manager import SimpleTokenManager
from src.api.enhanced_api_client import EnhancedAPIClient
from src.common.server.server_info import ServerInfoModel

# Test basic integration
def test_basic_integration():
    # Create server info (use your actual server)
    server_info = ServerInfoModel()
    server_info.data.server_ip = "*************"  # Your server IP
    server_info.data.server_port = 8080
    
    # Create enhanced API client
    api_client = EnhancedAPIClient(server_info)
    
    # Test login (use your actual credentials)
    response = api_client.login("your_username", "your_password")
    
    if response and response.status_code == 200:
        print("✅ Login successful with enhanced client")
        
        # Test API call
        cameras = api_client.get_cameras()
        if cameras:
            print("✅ API call successful with token management")
        else:
            print("❌ API call failed")
    else:
        print("❌ Login failed")

if __name__ == "__main__":
    test_basic_integration()
```

### **2. Run Integration Test**
```bash
python test_integration.py
```

### **3. Monitor Token Manager**
```python
# Add to your code for monitoring
from src.common.auth.simple_token_manager import SimpleTokenManager

def monitor_token_status():
    tm = SimpleTokenManager.get_instance()
    
    print("📊 Token Manager Status:")
    print(f"   Connected servers: {len(tm.get_connected_servers())}")
    print(f"   Total servers: {len(tm.get_all_servers())}")
    
    for server_id, server_info in tm.get_all_servers().items():
        print(f"   {server_id}: {server_info.auth_state.value}")
        
        if server_info.token:
            token = server_info.token
            if token.expires_at:
                time_left = token.time_until_expiry()
                if time_left:
                    minutes = int(time_left.total_seconds() // 60)
                    print(f"     Token expires in: {minutes} minutes")

# Call this periodically or in debug mode
monitor_token_status()
```

## 🚨 **TROUBLESHOOTING**

### **Common Issues:**

#### **1. Import Errors**
```bash
# If you get import errors:
# Make sure you're in the correct directory
cd c:\Users\<USER>\work2025\iVMS

# Check Python path
python -c "import sys; print(sys.path)"
```

#### **2. Missing Dependencies**
```bash
# If PySide6 not found:
pip install PySide6

# If websocket-client not found:
pip install websocket-client
```

#### **3. Token Manager Not Working**
```python
# Debug token manager
from src.common.auth.simple_token_manager import SimpleTokenManager

tm = SimpleTokenManager.get_instance()
print(f"Token manager: {tm}")
print(f"Servers: {tm.get_all_servers()}")
```

## ✅ **VERIFICATION CHECKLIST**

- [ ] Phase 1 test passes (`test_simple_token_manager.py`)
- [ ] Can create EnhancedAPIClient without errors
- [ ] Can create EnhancedWebsocketClient without errors
- [ ] Login stores tokens in SimpleTokenManager
- [ ] API calls work with token validation
- [ ] WebSocket connects only when server authenticated
- [ ] UI notifications appear (if integrated)
- [ ] Token expiration warnings work
- [ ] Refresh failure disconnects gracefully

## 🎯 **NEXT STEPS AFTER INTEGRATION**

1. **Test with real server** - Use actual iVMS server credentials
2. **Monitor logs** - Check for any authentication issues
3. **Test edge cases** - Token expiration, network issues, server restart
4. **Add custom UI** - Integrate status widgets where needed
5. **Performance testing** - Ensure no performance impact

## 📞 **SUPPORT**

If you encounter issues:
1. Check the logs for error messages
2. Verify server connectivity
3. Test with existing APIClient first
4. Run individual phase tests
5. Check token manager status

**The OAuth 2.0 solution is backward compatible - existing code should work unchanged with enhanced features added automatically!** ✅
